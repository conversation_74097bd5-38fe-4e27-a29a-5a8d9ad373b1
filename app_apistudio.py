from datetime import datetime
import json, os, copy, langfuse, uuid, time
import redis
import psycopg2.extras
from flask import Flask, render_template, request, jsonify
from langfuse import observe


# Import the new Celery task
from Check.Celery.tasks import process_check_task, get_embeddings_task
from celery.result import AsyncResult
from celery.exceptions import TimeoutError
from Qdrant.api.utils.db import get_cached_api_keys, refresh_api_keys_cache, get_db_connection as get_pg_connection
from DatabaseManagement.ImportExport import execute_gz_query, get_table_from_GZ

# Determine server identifier for Langfuse tracing
current_path = os.getcwd()
if 'workspace' in current_path:
    server_identifier = "Main"
elif 'app' in current_path:
    server_identifier = "Backup"
else:
    import socket
    server_identifier = socket.gethostname()
# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

# Load API keys from database with caching
# The global allowed_api_keys variable has been removed.
# Keys will now be fetched from the Redis cache on each request
# to ensure all workers have the most up-to-date information.
print("✅ API key validation will now use the Redis cache directly.")

# Create Flask app for standalone mode
standalone_app = Flask(__name__)

# In-memory state for rate limiting and API keys
api_key_usage = {}

# Redis client for rate limiting
redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)

# Lock for siglip embeddings to prevent multiple concurrent GPU calls

# Check API project
os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-a81ed7b3-cbf2-494a-a6ff-c00b21778891"


# Pre-load models and cache for Gunicorn --preload
# Models and cache are now loaded by Celery workers.
# The pre-loading mechanism here is no longer needed and has been removed
# to save memory in the Gunicorn master and worker processes.

# The ansi_to_html and capture_stdout functions have been moved to Check/Celery/tasks.py
# and are no longer needed in the main application file.

    

def create_error_response(error_code, message, details=None, status_code=400):
    """Creates a standardized error response."""
    response = {
        "error": {
            "error_code": error_code,
            "message": message
        }
    }
    if details:
        response["error"]["details"] = details
    return jsonify(response), status_code

# Function to check rate limit
def check_rate_limit(api_key, current_api_keys):
    """
    Checks and updates rate limits using Redis.
    Returns (True, None) on success.
    Returns (False, 'REASON') on failure.
    """
    config = current_api_keys.get(api_key, {})
    if not config:
        return (False, 'INVALID_API_KEY') # Should not happen if called after key check

    rate_limit = config.get('rate_limit', 1)
    daily_limit = config.get('daily_limit', 100)

    # Per-minute rate limit
    minute_key = f"rate_limit:{api_key}:minute"
    minute_count = redis_client.incr(minute_key)
    if minute_count == 1:
        redis_client.expire(minute_key, 60)
    
    if minute_count > rate_limit:
        return (False, 'RATE_LIMIT_MINUTE_EXCEEDED')

    # Daily rate limit
    day_key = f"rate_limit:{api_key}:day"
    daily_count = redis_client.incr(day_key)
    if daily_count == 1:
        redis_client.expire(day_key, 86400)

    if daily_count > daily_limit:
        return (False, 'RATE_LIMIT_DAILY_EXCEEDED')

    return (True, None)

# The process_check_with_db function has been moved into a Celery task
# in Check/Celery/tasks.py and is no longer needed here.

def get_estimated_completion_time(queue_name='celery'):
    """
    Calculates the estimated completion time based on queue length.
    Formula: 30s base + (position / 4 workers) * 30s per job.
    """
    try:
        queue_length = redis_client.llen(queue_name)
        # Estimated time in seconds
        estimated_time = 30 + (queue_length // 4) * 30
        return estimated_time
    except Exception as e:
        print(f"⚠️ Could not calculate estimated completion time: {e}")
        return None

def _create_success_response(check_id, result, trace_id):
    """Creates a standardized success response for a completed task."""
    print(f"✅✅✅ Results are sent to client for check_id: {check_id}")
    try:
        with langfuse.get_client().start_as_current_span(name="Results Fetched and Found", trace_context={"trace_id": trace_id}):
            langfuse.get_client().update_current_span(output={"✅✅✅ Results are sent to client at": datetime.now().isoformat()})
    except Exception as trace_error:
        print(f"⚠️ Failed to update Langfuse trace for check_id: {check_id}, error: {str(trace_error)}")
    return jsonify({'status': 'completed', 'result': result})

def _create_failure_response(check_id, error_info):
    """Creates a standardized failure response from Celery task info."""
    error_code = 'JOB_FAILED'
    message = "The analysis job failed unexpectedly."
    details = str(error_info)
    error_dict = None
    if isinstance(error_info, dict):
        error_dict = error_info
    elif isinstance(error_info, Exception) and len(error_info.args) > 0 and isinstance(error_info.args[0], dict):
        error_dict = error_info.args[0]
    if error_dict:
        error_code = error_dict.get('error_code', 'JOB_FAILED')
        message = error_dict.get('exc_message', "The analysis job failed unexpectedly.")
        details = error_dict.get('details', str(error_info))
    print(f"🔥 Task failed for check_id: {check_id}. Reason: {details}")
    return jsonify({'status': 'error', 'error_code': error_code, 'message': message, 'details': details})

def check_status(check_id):
    """
    Checks the status of a Celery task. Uses long polling for processing tasks.
    """
    task_result = AsyncResult(check_id)
    trace_id = langfuse.get_client().create_trace_id(seed=check_id)

    # --- Handle final states first ---
    if task_result.state == 'SUCCESS':
        return _create_success_response(check_id, task_result.result, trace_id)
    
    elif task_result.state == 'FAILURE':
        return _create_failure_response(check_id, task_result.info)

    # --- Handle queued state ---
    elif task_result.state == 'PENDING':
        estimated_time = get_estimated_completion_time()
        response_data = {'status': 'queued', 'message': 'Your request is in the queue.'}
        if estimated_time is not None:
            response_data['estimated_completion_time'] = estimated_time
        return jsonify(response_data)

    # --- Handle processing state ---
    elif task_result.state in ['STARTED', 'PROCESSING']:
        # The client is polling, so we just return the current status without blocking.
        return jsonify({'status': 'processing', 'message': 'Your request is being processed.'})

    # --- Fallback for unknown task ID ---
    else:
        if not check_id.isdigit():
            print(f"Task not found for non-numeric check_id: {check_id}. Cannot check database.")
            return create_error_response('RESULTS_NOT_FOUND', "No results found for the given Check ID.", status_code=404)

        print(f"Celery task not found for numeric check_id: {check_id}. The tasks status is {task_result.state}. Checking database.")
        pg_conn = None
        try:
            pg_conn = get_pg_connection()
            pg_cursor = pg_conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
            pg_query = "SELECT result FROM check_api_results WHERE check_id = %s"
            pg_cursor.execute(pg_query, (int(check_id),))
            pg_data = pg_cursor.fetchone()
            pg_cursor.close()
            pg_conn.close()

            if pg_data:
                return _create_success_response(check_id, pg_data['result'], trace_id)
            else:
                return create_error_response('RESULTS_NOT_FOUND', "No results found for the given Check ID.", status_code=404)
        except Exception as e:
            error_message = f"Database error while retrieving results: {str(e)}"
            print(f"🔥 Error in check_status: {error_message}")
            return create_error_response('DATABASE_CONNECTION_ERROR', "Could not connect to the database.", details=error_message, status_code=500)
        finally:
            if pg_conn:
                try: pg_conn.close()
                except: pass

@observe(name="/check_api")
def check_api(check_id):
    data = request.get_json()
    trace_id = langfuse.get_client().get_current_trace_id()
    
    # Create a copy of data for logging to avoid modifying the original
    log_data = copy.deepcopy(data) if data else {}
    
    # Identify and truncate likely base64 image strings for logging
    for key, value in log_data.items() if isinstance(log_data, dict) else []:
        if key in ['main_product_image', 'other_product_images', 'ip_images', 'reference_images']:
            # Handle array of images or direct image strings
            if isinstance(value, list):
                for i in range(len(value)):
                    if isinstance(value[i], str) and len(value[i]) > 500:
                        log_data[key][i] = f"[base64 image: {len(value[i])} chars]"
            elif isinstance(value, str) and len(value) > 500:
                log_data[key] = f"[base64 image: {len(value)} chars]"

    print(f"\n\n📥📥📥 API request received at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: Parsed JSON data: {log_data}")
    
    try:
        # Retrieve and validate API key first
        allowed_api_keys = get_cached_api_keys()
        api_key = data.get('api_key')
        if not api_key or api_key not in allowed_api_keys:
            print(f"⛔ API authentication failed for {api_key}: Invalid API Key.")
            return create_error_response('INVALID_API_KEY', "The provided API Key is invalid or does not exist.", status_code=401)
        
        client_info = allowed_api_keys.get(api_key)
            
        # Check rate limit before processing
        is_allowed, reason = check_rate_limit(api_key, allowed_api_keys)
        if not is_allowed:
            if reason == 'RATE_LIMIT_MINUTE_EXCEEDED':
                message = "You have exceeded the per-minute request limit."
            elif reason == 'RATE_LIMIT_DAILY_EXCEEDED':
                message = "You have exceeded your daily request limit."
            else:
                message = "Rate limit exceeded."
            print(f"⛔ API rate limit exceeded for {api_key}: {reason}")
            return create_error_response(reason, message, status_code=429)
        
        langfuse.get_client().update_current_trace(name=f"[{server_identifier}] API Check", user_id=client_info['client_name'], session_id=check_id, metadata={"check_id": check_id, "client": client_info['client_name'], "api_key": api_key, "server": server_identifier})
        langfuse.get_client().update_current_span(input=log_data)
        print(f"📊 Langfuse trace_id for check_id {check_id}: {langfuse.get_client().get_current_trace_id()}")
        print(f"📊 Updated Langfuse trace with check_id: {check_id} for client: {client_info['client_name']}")
    
        # Retrieve and validate other required fields.
        product_category = data.get('product_category', '') or ""
        main_product_image = data.get('main_product_image')
        if not main_product_image:
            print(f"⛔ API validation error: Missing main_product_image.")
            return create_error_response('MISSING_MAIN_IMAGE', "A main product image is required for the analysis.", status_code=400)
            
        other_product_images = data.get('other_product_images', []) or []
        client_ip_images = data.get('ip_images', []) or []
        ip_keywords = data.get('ip_keywords', []) or []
        description = data.get('description', '')
            
        reference_text = data.get('reference_text', '') or ""
        reference_images = data.get('reference_images', []) or []
        language = data.get('language', 'zh')
        
    except KeyError as e:
        field_name = str(e).strip("'")
        error_message = f"Missing required field: {field_name}"
        print(f"⛔ API validation error: {error_message}")
        return create_error_response('MISSING_REQUIRED_FIELD', error_message, details=f"The field '{field_name}' is required.", status_code=400)
    except Exception as e:
        error_message = f"Internal server error: {str(e)}"
        print(f"🔥 API server error: {error_message}")
        return create_error_response('SERVER_ERROR', "An unexpected internal server error occurred.", details=str(e), status_code=500)


    try:
        # Submit the job to the Celery queue.
        # .delay() is a shortcut to .apply_async().
        # We pass all the necessary arguments to the task.
        # The task_id is explicitly set to our generated check_id for easy tracking.
        # --- Start Data Integrity Logging ---
        if main_product_image:
            sender_debug_info = {
                "sender_image_len": len(main_product_image),
                "sender_image_hash": hash(main_product_image)
            }
            print(f"DEBUG: [SENDER] check_id={check_id}, {sender_debug_info}")
            langfuse.get_client().update_current_span(metadata=sender_debug_info)
        # --- End Data Integrity Logging ---

        # Check for debug mode via environment variable
        if os.getenv('DEBUG_SYNC_MODE', 'false').lower() == 'true':
            # --- Synchronous Execution for Debugging ---
            print("DEBUG: Running in synchronous mode.")
            try:
                result = process_check_task(
                    check_id=check_id,
                    client_id=client_info['id'],
                    client_name=client_info['client_name'],
                    langfuse_trace_id=trace_id,
                    main_product_image=main_product_image,
                    other_product_images=other_product_images,
                    client_ip_images=client_ip_images,
                    ip_keywords=ip_keywords,
                    description=description,
                    reference_text=reference_text,
                    reference_images=reference_images,
                    product_category=product_category,
                    api_key=api_key,
                    language=language
                )
                # Return a response that mimics the final polling result,
                # which the frontend can handle directly.
                return jsonify({
                    'check_id': check_id,
                    'status': 'completed',
                    'result': result
                })
            except Exception as e:
                # Handle exceptions during synchronous execution
                error_message = f"Error during synchronous task execution: {str(e)}"
                print(f"🔥 {error_message}")
                return create_error_response('SYNC_TASK_ERROR', "An error occurred during synchronous processing.", details=str(e), status_code=500)
        else:
            # --- Asynchronous Execution (Normal Operation) ---
            process_check_task.apply_async(
                args=[check_id, client_info['id'], client_info['client_name'], trace_id],
                kwargs={
                    'main_product_image': main_product_image,
                    'other_product_images': other_product_images,
                    'client_ip_images': client_ip_images,
                    'ip_keywords': ip_keywords,
                    'description': description,
                    'reference_text': reference_text,
                    'reference_images': reference_images,
                    'product_category': product_category,
                    'api_key': api_key,
                    'language': language,
                },
                task_id=check_id
            )

            # Return an immediate response with an estimated completion time.
            estimated_time = get_estimated_completion_time()
            response_data = {
                'check_id': check_id,
                'status': 'queued',  # More accurate initial status
                'message': 'Analysis has been queued. Use the check_status endpoint to poll for results.'
            }
            if estimated_time is not None:
                response_data['estimated_completion_time'] = estimated_time
            
            return jsonify(response_data)
    except Exception as e:
        error_message = f"Error submitting task to Celery: {str(e)}"
        print(f"🔥 API task submission error: {error_message}")
        return create_error_response('TASK_SUBMISSION_ERROR', "There was an error submitting your request for processing.", details=str(e), status_code=500)

# Define routes for standalone mode
@standalone_app.route('/api_studio')
def api_studio_route():
    return api_studio()

@standalone_app.route('/api_studio_reverse_check')
def api_studio_reverse_check_route():
    return api_studio_reverse_check()

def api_studio():
    return render_template('api_studio.html')

def api_studio_reverse_check():
    return render_template('api_studio_reverse_check.html')

@standalone_app.route('/check_history')
def check_history_route():
    return render_template('check_history.html')

@standalone_app.route('/get_check_dates', methods=['POST'])
def get_check_dates():
    data = request.get_json()
    api_key = data.get('api_key')
    admin_api_key = os.getenv("ADMIN_API_KEY")

    is_admin = admin_api_key and api_key == admin_api_key

    allowed_api_keys = get_cached_api_keys()
    if not is_admin and (not api_key or api_key not in allowed_api_keys):
        return jsonify({"error": "Invalid API Key"}), 401

    connection = None
    try:
        connection = get_pg_connection()
        cursor = connection.cursor()
        
        if is_admin:
            # 1. Get dates from the primary PG database
            query_pg = """
                SELECT DISTINCT DATE(create_time)
                FROM check_api_requests
            """
            cursor.execute(query_pg)
            dates_pg = {row[0].strftime('%Y-%m-%d') for row in cursor.fetchall()}

            # 2. Get dates from the secondary GZ (MySQL) database
            try:
                query_gz = "SELECT DISTINCT DATE(create_time) FROM tb_case_check"
                gz_results = execute_gz_query(query_gz)
                dates_gz = {row[0].strftime('%Y-%m-%d') for row in gz_results if row[0]}
            except Exception as e:
                print(f"⚠️ Could not fetch dates from GZ database: {e}")
                dates_gz = set()

            # 3. Merge, sort, and return
            all_dates = sorted(list(dates_pg.union(dates_gz)), reverse=True)
            return jsonify({"dates": all_dates})

        else:
            query = """
                SELECT DISTINCT DATE(create_time)
                FROM check_api_requests
                WHERE api_key = %s
                ORDER BY DATE(create_time) DESC
            """
            cursor.execute(query, (api_key,))
            dates = [row[0].strftime('%Y-%m-%d') for row in cursor.fetchall()]
            return jsonify({"dates": dates})
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        if connection:
            cursor.close()
            connection.close()

@standalone_app.route('/get_checks_for_date', methods=['POST'])
def get_checks_for_date():
    data = request.get_json()
    api_key = data.get('api_key')
    date = data.get('date')
    admin_api_key = os.getenv("ADMIN_API_KEY")

    is_admin = admin_api_key and api_key == admin_api_key

    allowed_api_keys = get_cached_api_keys()
    if not is_admin and (not api_key or api_key not in allowed_api_keys):
        return jsonify({"error": "Invalid API Key"}), 401

    connection = None
    try:
        connection = get_pg_connection()
        cursor = connection.cursor()
        
        if is_admin:
            # 1. Get checks from the primary PG database
            query_pg = """
                SELECT id, create_time, api_key
                FROM check_api_requests
                WHERE DATE(create_time) = %s
                ORDER BY create_time DESC
            """
            cursor.execute(query_pg, (date,))
            checks_from_db = cursor.fetchall()
            
            checks = []
            for row in checks_from_db:
                check_id, timestamp, api_key_from_db = row
                client_info = get_cached_api_keys().get(api_key_from_db, {})
                client_name = client_info.get('client_name', 'Unknown User')
                checks.append({
                    "check_id": str(check_id),
                    "timestamp": timestamp.isoformat(),
                    "client_name": client_name
                })

            # 2. Get checks from the secondary GZ (MySQL) database
            try:
                where_clause = f"DATE(create_time) = '{date}'"
                df_gz = get_table_from_GZ('tb_case_check', force_refresh=True, where_clause=where_clause)
                
                for index, row in df_gz.iterrows():
                    checks.append({
                        "check_id": f"gz_{row['id']}",
                        "timestamp": row['create_time'].isoformat(),
                        "client_name": "GZ_User"  # Or another identifier
                    })
            except Exception as e:
                print(f"⚠️ Could not fetch checks from GZ database for date {date}: {e}")

            # 3. Sort combined checks
            checks.sort(key=lambda x: x['timestamp'], reverse=True)
            checks.sort(key=lambda x: x['client_name'])

        else:
            query = """
                SELECT id, create_time
                FROM check_api_requests
                WHERE DATE(create_time) = %s
                AND api_key = %s
                ORDER BY create_time DESC
            """
            cursor.execute(query, (date, api_key))
            checks = [{"check_id": str(row[0]), "timestamp": row[1].isoformat()} for row in cursor.fetchall()]
            
        return jsonify({"checks": checks})
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        if connection:
            cursor.close()
            connection.close()

@standalone_app.route('/get_check_details', methods=['POST'])
def get_check_details():
    data = request.get_json()
    api_key = data.get('api_key')
    check_id = data.get('check_id')
    admin_api_key = os.getenv("ADMIN_API_KEY")

    is_admin = admin_api_key and api_key == admin_api_key

    allowed_api_keys = get_cached_api_keys()
    if not is_admin and (not api_key or api_key not in allowed_api_keys):
        return jsonify({"error": "Invalid API Key"}), 401

    # Handle checks from the GZ (MySQL) database
    if is_admin and isinstance(check_id, str) and check_id.startswith('gz_'):
        try:
            gz_check_id = check_id.split('gz_')[1]
            
            # Fetch request data from tb_case_check
            where_clause_request = f"id = {gz_check_id}"
            df_request = get_table_from_GZ('tb_case_check', force_refresh=True, where_clause=where_clause_request)
            
            if df_request.empty:
                return jsonify({"error": "GZ check not found"}), 404
            
            request_data = df_request.iloc[0].to_dict()
            
            # Fetch result data from tb_case_check_result
            where_clause_result = f"check_id = {gz_check_id}"
            df_result = get_table_from_GZ('tb_case_check_result', force_refresh=True, where_clause=where_clause_result)
            
            result_data = df_result.iloc[0].to_dict() if not df_result.empty else {}

            # Map GZ data to the expected format
            # This is a sample mapping, adjust as per actual column names
            request_dict = {
                'id': f"gz_{request_data.get('id')}",
                'create_time': request_data.get('create_time').isoformat() if request_data.get('create_time') else None,
                'product_category': request_data.get('product_category'),
                'description': request_data.get('description'),
                'ip_keywords': request_data.get('ip_keywords'),
                'reference_text': request_data.get('reference_text'),
                'main_product_image': request_data.get('main_product_image'),
                'other_product_images': request_data.get('other_product_images'),
                'ip_images': request_data.get('ip_images'),
                'reference_images': request_data.get('reference_images'),
                'api_key': "GZ_DB" # Placeholder for API key
            }

            return jsonify({
                "request": request_dict,
                "result": json.loads(result_data['result']) if result_data and result_data.get('result') and isinstance(result_data.get('result'), str) else result_data.get('result')
            })

        except Exception as e:
            return jsonify({"error": f"Error fetching GZ check details: {str(e)}"}), 500

    # Existing logic for PG database
    connection = None
    try:
        connection = get_pg_connection()
        cursor = connection.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Fetch request data
        if is_admin:
            query_request = "SELECT * FROM check_api_requests WHERE id = %s"
            cursor.execute(query_request, (int(check_id),))
        else:
            query_request = "SELECT * FROM check_api_requests WHERE id = %s AND api_key = %s"
            cursor.execute(query_request, (int(check_id), api_key))
        
        request_data = cursor.fetchone()

        # Fetch result data
        query_result = "SELECT result FROM check_api_results WHERE check_id = %s"
        cursor.execute(query_result, (int(check_id),))
        result_data = cursor.fetchone()

        if not request_data:
            return jsonify({"error": "Check not found or access denied"}), 404

        request_dict = dict(request_data) if request_data else None
        if request_dict and request_dict.get('id'):
            request_dict['id'] = str(request_dict['id'])

        return jsonify({
            "request": request_dict,
            "result": result_data['result'] if result_data and result_data.get('result') else None
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        if connection:
            cursor.close()
            connection.close()


@standalone_app.route('/check_status/<check_id>')
def check_status_route(check_id):
    return check_status(check_id)

@standalone_app.route('/check_api', methods=['POST'])
def check_api_route():
    now = datetime.now()
    data = request.get_json()
    api_key = data.get('api_key')

    # Validate API key before proceeding to prevent crash
    allowed_api_keys = get_cached_api_keys()
    if not api_key or api_key not in allowed_api_keys:
        print(f"⛔ API authentication failed for invalid key '{api_key}' in check_api_route.")
        return create_error_response(
            'INVALID_API_KEY',
            "The provided API Key is invalid or does not exist.",
            status_code=401
        )

    client_info = allowed_api_keys.get(api_key)
    if client_info["client_name"] in ["MiniApp", "H5"]:
        check_id = data.get('check_id')
    else:
        check_id = f"{client_info['id']}{now.strftime('%Y%m%d%H%M%S')}"
    
    print(f"Generated check_id: {check_id}")
    trade_id = langfuse.get_client().create_trace_id(seed=check_id)
    print(f"Generated Langfuse trace_id: {trade_id}")
    return check_api(check_id=check_id, langfuse_trace_id=trade_id)

@standalone_app.route('/reverse_check_status', methods=['POST'])
def reverse_check_status_route():
    """Route to relay reverse check status requests to the Qdrant API"""
    import requests

    # Get the Qdrant API URL from environment variable
    qdrant_api_url = os.getenv("QDRANT_API_URL")
    if not qdrant_api_url:
        return jsonify({"error": "QDRANT_API_URL environment variable not set"}), 500

    # Relay the request to the Qdrant API
    try:
        response = requests.post(
            f"{qdrant_api_url}/reverse_check_status",
            json=request.get_json(),
            headers={'Content-Type': 'application/json'},
            timeout=30
        )

        # Return the response from the Qdrant API
        return jsonify(response.json()), response.status_code

    except requests.exceptions.RequestException as e:
        return jsonify({"error": f"Failed to connect to Qdrant API: {str(e)}"}), 500
    except Exception as e:
        return jsonify({"error": f"Server error: {str(e)}"}), 500

@standalone_app.route('/get_image_embeddings', methods=['POST'])
def get_image_embeddings_route():
    data = request.get_json()

    # API Key validation
    api_key = data.get('api_key')
    if not api_key or api_key != os.environ.get("API_BEARER_TOKEN"):
        error_message = "Invalid API Key. Authentication failed."
        print(f"⛔ API authentication failed for {api_key}: {error_message}")
        return jsonify({'error': error_message, 'status': 'failed', 'error_code': 'AUTH_FAILED'}), 401

    try:
        images_base64 = data.get('images', [])
        if not images_base64:
            error_message = "Missing required field: 'images' (list of base64 encoded images)"
            print(f"⛔ API validation error: {error_message}")
            return jsonify({'error': error_message,'status': 'failed','error_code': 'MISSING_REQUIRED_FIELD'}), 400

        # Generate a unique task ID for tracking
        task_id = str(uuid.uuid4())

        # Asynchronously call the Celery task
        get_embeddings_task.apply_async(
            args=[images_base64],
            task_id=task_id
        )

        # Immediately return a response with the task_id
        return jsonify({
            'status': 'processing',
            'task_id': task_id,
            'message': 'Embedding generation has been queued. Use the /embedding_status/<task_id> endpoint to poll for results.'
        })

    except Exception as e:
        error_message = f"Internal server error during embedding task submission: {str(e)}"
        print(f"🔥 API processing error: {error_message}")
        return jsonify({
            'error': error_message,
            'status': 'failed',
            'error_code': 'TASK_SUBMISSION_ERROR',
            'details': str(e)
        }), 500

@standalone_app.route('/embedding_status/<task_id>')
def embedding_status_route(task_id):
    """Checks the status of an embedding generation task."""
    task_result = AsyncResult(task_id)
    
    if task_result.state == 'SUCCESS':
        # Task completed successfully, return the result
        return jsonify({'status': 'completed', 'result': task_result.get()})
    elif task_result.state == 'FAILURE':
        # Task failed, return an error message
        return jsonify({'status': 'error', 'message': 'Task failed during embedding generation.'}), 500
    else:
        # Task is still pending or processing
        return jsonify({'status': 'processing', 'message': 'Your request is being processed.'})

@standalone_app.route('/admin/refresh-api-keys', methods=['POST'])
def refresh_api_keys_route():
    """
    Admin endpoint to manually refresh the API keys cache from the database.
    Requires the admin bearer token for authorization, passed in the 'Authorization' header.
    To trigger it run: curl -X POST https://api.maildalv.com:5088/admin/refresh-api-keys -H "Authorization: Bearer your_api_bearer_token_here"
    """
    auth_header = request.headers.get('Authorization')
    expected_token = os.getenv("API_BEARER_TOKEN")

    if not expected_token:
        print("🔥 CRITICAL: API_BEARER_TOKEN is not configured on the server.")
        return create_error_response(
            'ADMIN_KEY_NOT_CONFIGURED',
            "The admin bearer token is not configured on the server.",
            status_code=500
        )

    if not auth_header or not auth_header.startswith('Bearer '):
        print(f"⛔ Unauthorized attempt to refresh API keys (missing or malformed header) from IP: {request.remote_addr}")
        return create_error_response(
            'UNAUTHORIZED',
            "Invalid or missing Authorization header. Expected 'Bearer <token>'.",
            status_code=401
        )
    
    provided_token = auth_header.split(' ')[1]

    if provided_token != expected_token:
        print(f"⛔ Unauthorized attempt to refresh API keys (invalid token) from IP: {request.remote_addr}")
        return create_error_response(
            'UNAUTHORIZED',
            "Invalid admin bearer token.",
            status_code=401
        )

    try:
        refreshed_keys = refresh_api_keys_cache()
        count = len(refreshed_keys)
        print(f"✅ API keys cache refreshed successfully by admin request. Loaded {count} keys.")
        return jsonify({
            'status': 'success',
            'message': f"Successfully refreshed API keys cache. {count} keys loaded."
        })
    except Exception as e:
        error_message = f"Failed to refresh API keys cache: {str(e)}"
        print(f"🔥 {error_message}")
        return create_error_response(
            'CACHE_REFRESH_FAILED', 
            "An error occurred while refreshing the cache.", 
            details=str(e), 
            status_code=500
        )

# Standalone server: not for check (because we do not load the models) but only for reverse check or check history
if __name__ == '__main__':
    import os
    from waitress import serve

    # os.environ['DEBUG_SYNC_MODE'] = 'True'

    print("Starting API Studio Server...")
    serve(
        standalone_app,
        host="0.0.0.0",
        port=5000,  # if you change the port, you need to change in Javascript also
        threads=10,  # The number of requests Waitress can actively process at the same time. This is your primary concurrency control.
        connection_limit=40,  # The maximum number of connections Waitress will accept, including those being actively processed and those waiting in its internal queue.
        channel_timeout=900
    )

### DEBUG MODE: 
# Set DEBUG_SYNC_MODE to True
# Change port to 5001 (if real app already running on 5000)
# F5 on app apistudio
# Tips: best to run in container due to database download for Trademark text
# Note: in debug mode it will use the automaton in trademarks_precomputed_marks_dev