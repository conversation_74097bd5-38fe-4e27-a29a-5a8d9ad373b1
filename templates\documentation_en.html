<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maidalv IP Check API Documentation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" id="prism-dark-theme" disabled>
    <style>
        :root {
            --primary-color: #1a73e8;
            --primary-hover: #1557b0;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --border-color: #dadce0;
            --background-light: #f8f9fa;
            --background-white: #ffffff;
            --code-background: #f8f9fa;
            --shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --shadow-hover: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
        }

        [data-theme="dark"] {
            --text-primary: #e8eaed;
            --text-secondary: #9aa0a6;
            --border-color: #3c4043;
            --background-light: #202124;
            --background-white: #303134;
            --code-background: #2d2d30;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--background-light);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 280px;
            background: var(--background-white);
            border-right: 1px solid var(--border-color);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 100;
            transition: background-color 0.3s ease;
        }

        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-size: 20px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .theme-toggle {
            background: none;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 6px 8px;
            cursor: pointer;
            color: var(--text-secondary);
            transition: all 0.2s ease;
        }

        .theme-toggle:hover {
            background: var(--background-light);
            color: var(--text-primary);
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-item {
            display: block;
            padding: 12px 20px;
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-item:hover {
            background: var(--background-light);
            color: var(--text-primary);
        }

        .nav-item.active {
            color: var(--primary-color);
            background: rgba(26, 115, 232, 0.08);
            border-left-color: var(--primary-color);
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            flex: 1;
            padding: 40px;
            max-width: calc(100vw - 280px);
        }

        .content-header {
            margin-bottom: 40px;
        }

        .content-header h1 {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .content-header p {
            font-size: 18px;
            color: var(--text-secondary);
            max-width: 600px;
        }

        /* Sections */
        .section {
            margin-bottom: 48px;
        }

        .section h2 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--text-primary);
            scroll-margin-top: 20px;
        }

        .section h3 {
            font-size: 20px;
            font-weight: 600;
            margin: 32px 0 16px 0;
            color: var(--text-primary);
            scroll-margin-top: 20px;
        }

        .section h4 {
            font-size: 16px;
            font-weight: 600;
            margin: 24px 0 12px 0;
            color: var(--text-primary);
        }

        .section p {
            margin-bottom: 16px;
            color: var(--text-secondary);
            line-height: 1.7;
        }

        .section ul {
            margin: 16px 0;
            padding-left: 24px;
        }

        .section li {
            margin-bottom: 8px;
            color: var(--text-secondary);
        }

        /* Code Blocks */
        .code-container {
            position: relative;
            margin: 24px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .code-header {
            background: var(--background-white);
            border-bottom: 1px solid var(--border-color);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .code-tabs {
            display: flex;
            gap: 8px;
        }

        .code-tab {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--background-light);
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .code-tab.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .copy-button {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--background-light);
            color: var(--text-secondary);
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .copy-button:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .copy-button.copied {
            background: #34a853;
            color: white;
            border-color: #34a853;
        }

        pre[class*="language-"] {
            margin: 0;
            border-radius: 0;
            background: var(--code-background) !important;
        }

        code[class*="language-"] {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        .code-block {
            display: none;
        }

        .code-block.active {
            display: block;
        }

        /* Tables */
        .table-container {
            overflow-x: auto;
            margin: 24px 0;
            border-radius: 8px;
            box-shadow: var(--shadow);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: var(--background-white);
        }

        th, td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: var(--background-light);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 14px;
        }

        td {
            color: var(--text-secondary);
            font-size: 14px;
        }

        tr:last-child td {
            border-bottom: none;
        }

        /* Badges */
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .badge.post {
            background: #e8f5e8;
            color: #137333;
        }

        .badge.get {
            background: #e3f2fd;
            color: #1565c0;
        }

        .badge.required {
            background: #fce8e6;
            color: #d93025;
        }

        .badge.optional {
            background: #f3f4f6;
            color: #6b7280;
        }

        /* Alert Boxes */
        .alert {
            padding: 16px;
            border-radius: 8px;
            margin: 24px 0;
            border-left: 4px solid;
        }

        .alert.info {
            background: #e3f2fd;
            border-color: #2196f3;
            color: #0d47a1;
        }

        .alert.warning {
            background: #fff3e0;
            border-color: #ff9800;
            color: #e65100;
        }

        .alert.success {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">Maidalv API</div>
                <button class="theme-toggle" onclick="toggleTheme()" title="Toggle theme">
                    🌙
                </button>
            </div>
            <nav class="nav-menu">
                <a href="#overview" class="nav-item active">Overview</a>
                <a href="#authentication" class="nav-item">Authentication</a>
                <a href="#submit-check" class="nav-item">Submit Check</a>
                <a href="#retrieve-results" class="nav-item">Retrieve Results</a>
                <a href="#errors" class="nav-item">Error Handling</a>
                <a href="#rate-limiting" class="nav-item">Rate Limiting</a>
                <a href="#faq" class="nav-item">FAQ</a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1>IP Check API Documentation</h1>
                <p>Comprehensive guide to integrating with the Maidalv Intellectual Property Check API</p>
            </div>

            <!-- Overview Section -->
            <section id="overview" class="section">
                <h2>Overview</h2>
                <p>This API service helps ecommerce sellers quickly check whether their products may infringe on the intellectual property (IP) of another party — covering copyrights, trademarks, and patents.</p>

                <h3>What You Can Submit</h3>
                <ul>
                    <li><strong>Product images</strong> - Images of the product you want to check</li>
                    <li><strong>IP images</strong> - Any image of intellectual property used in making the product</li>
                    <li><strong>Reference images</strong> - Similar products that inspired the design</li>
                    <li><strong>Product description</strong> - Text description of your product</li>
                    <li><strong>IP keywords</strong> - Relevant keywords related to intellectual property</li>
                </ul>

                <h3>What You Get Back</h3>
                <ul>
                    <li><strong>Infringement analysis</strong> - The most likely infringements identified</li>
                    <li><strong>IP evidence</strong> - Images of the infringed intellectual property</li>
                    <li><strong>Court case history</strong> - Information on whether that IP has been used in court cases</li>
                    <li><strong>Legal opinion report</strong> - Assessment of the severity of potential infringement</li>
                    <li><strong>Risk assessment</strong> - Overall risk level (High, Medium, Low)</li>
                </ul>

                <p>This enables sellers to make informed decisions before listing or selling their products.</p>

                <div class="alert info">
                    <strong>Base URL:</strong> <code>{BASE_URL}</code><br>
                    Replace <code>{BASE_URL}</code> with your actual API endpoint URL.
                </div>
            </section>

            <!-- Authentication Section -->
            <section id="authentication" class="section">
                <h2>Authentication</h2>
                <p>All API requests require authentication using an API key. Include your API key in the request body as the <code>api_key</code> field.</p>

                <div class="alert warning">
                    <strong>Security Note:</strong> Keep your API key secure and never expose it in client-side code. Always make API calls from your server.
                </div>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests

# Your API key
API_KEY = "{YOUR_API_KEY}"

# Include in request body
data = {
    "api_key": API_KEY,
    # ... other parameters
}</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">// Your API key
const API_KEY = "{YOUR_API_KEY}";

// Include in request body
const data = {
    api_key: API_KEY,
    // ... other parameters
};</code></pre>
                    </div>
                </div>
            </section>

            <!-- Submit Check Section -->
            <section id="submit-check" class="section">
                <h2>Submit a Product for IP Check</h2>
                <p><span class="badge post">POST</span> <code>/check_api</code></p>

                <p>Submit a product for intellectual property analysis. This endpoint accepts product images, descriptions, and related information to perform comprehensive IP checking.</p>

                <h3>Request Parameters</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>api_key</code></td>
                                <td>string</td>
                                <td><span class="badge required">Required</span></td>
                                <td>Your API authentication key</td>
                            </tr>
                            <tr>
                                <td><code>main_product_image</code></td>
                                <td>string</td>
                                <td><span class="badge required">Required</span></td>
                                <td>Base64 encoded image or image URL of the main product</td>
                            </tr>
                            <tr>
                                <td><code>other_product_images</code></td>
                                <td>array</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Array of additional product images (base64 or URLs)</td>
                            </tr>
                            <tr>
                                <td><code>ip_images</code></td>
                                <td>array</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Array of IP-related images (base64 or URLs)</td>
                            </tr>
                            <tr>
                                <td><code>reference_images</code></td>
                                <td>array</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Array of reference images (base64 or URLs)</td>
                            </tr>
                            <tr>
                                <td><code>description</code></td>
                                <td>string</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Text description of the product</td>
                            </tr>
                            <tr>
                                <td><code>ip_keywords</code></td>
                                <td>array</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Array of IP-related keywords</td>
                            </tr>
                            <tr>
                                <td><code>reference_text</code></td>
                                <td>string</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Additional reference text</td>
                            </tr>
                            <tr>
                                <td><code>product_category</code></td>
                                <td>string</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Category of the product</td>
                            </tr>
                            <tr>
                                <td><code>language</code></td>
                                <td>string</td>
                                <td><span class="badge optional">Optional</span></td>
                                <td>Response language ('en' or 'zh', default: 'zh')</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Example Request</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests
import base64
import json

# Your API configuration
API_KEY = "{YOUR_API_KEY}"
BASE_URL = "{BASE_URL}"

# Function to encode image to base64
def encode_image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

# Prepare the request data
data = {
    "api_key": API_KEY,
    "main_product_image": encode_image_to_base64("product.jpg"),
    "other_product_images": [
        encode_image_to_base64("product2.jpg"),
        encode_image_to_base64("product3.jpg")
    ],
    "ip_images": [
        encode_image_to_base64("ip_reference.jpg")
    ],
    "description": "Wireless bluetooth headphones with noise cancellation",
    "ip_keywords": ["bluetooth", "wireless", "headphones", "noise cancellation"],
    "reference_text": "Similar to popular brand headphones",
    "product_category": "Electronics",
    "language": "en"
}

# Submit the check request
try:
    response = requests.post(
        f"{BASE_URL}/check_api",
        json=data,
        headers={"Content-Type": "application/json"},
        timeout=30
    )

    if response.status_code == 200:
        result = response.json()
        check_id = result.get("check_id")
        status = result.get("status")

        print(f"Check submitted successfully!")
        print(f"Check ID: {check_id}")
        print(f"Status: {status}")

        if "estimated_completion_time" in result:
            print(f"Estimated completion time: {result['estimated_completion_time']} seconds")

    else:
        error_data = response.json()
        print(f"Error: {error_data}")

except requests.exceptions.RequestException as e:
    print(f"Request failed: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">// Your API configuration
const API_KEY = "{YOUR_API_KEY}";
const BASE_URL = "{BASE_URL}";

// Function to convert file to base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result.split(',')[1]);
        reader.onerror = error => reject(error);
    });
}

// Example with file inputs (browser environment)
async function submitCheck() {
    try {
        // Get files from input elements
        const mainImageFile = document.getElementById('mainImage').files[0];
        const otherImageFiles = document.getElementById('otherImages').files;

        // Convert to base64
        const mainImageBase64 = await fileToBase64(mainImageFile);
        const otherImagesBase64 = [];

        for (let file of otherImageFiles) {
            const base64 = await fileToBase64(file);
            otherImagesBase64.push(base64);
        }

        // Prepare request data
        const data = {
            api_key: API_KEY,
            main_product_image: mainImageBase64,
            other_product_images: otherImagesBase64,
            description: "Wireless bluetooth headphones with noise cancellation",
            ip_keywords: ["bluetooth", "wireless", "headphones", "noise cancellation"],
            reference_text: "Similar to popular brand headphones",
            product_category: "Electronics",
            language: "en"
        };

        // Submit the request
        const response = await fetch(`${BASE_URL}/check_api`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            const result = await response.json();
            console.log('Check submitted successfully!');
            console.log('Check ID:', result.check_id);
            console.log('Status:', result.status);

            if (result.estimated_completion_time) {
                console.log('Estimated completion time:', result.estimated_completion_time, 'seconds');
            }

            return result;
        } else {
            const errorData = await response.json();
            console.error('Error:', errorData);
            throw new Error(errorData.error?.message || 'Request failed');
        }

    } catch (error) {
        console.error('Request failed:', error);
        throw error;
    }
}

// Call the function
submitCheck()
    .then(result => {
        // Handle successful submission
        console.log('Submission result:', result);
    })
    .catch(error => {
        // Handle errors
        console.error('Submission failed:', error);
    });</code></pre>
                    </div>
                </div>

                <div class="disclosure">
                    <div class="disclosure-header" onclick="toggleDisclosure(this)">
                        <span>Show cURL example</span>
                        <span class="disclosure-arrow">▶</span>
                    </div>
                    <div class="disclosure-content">
                        <div class="code-container">
                            <div class="code-header">
                                <div class="code-tabs">
                                    <button class="code-tab active" data-lang="bash">cURL</button>
                                </div>
                                <button class="copy-button" onclick="copyCode(this)">Copy</button>
                            </div>
                            <div class="code-block active" data-lang="bash">
                                <pre><code class="language-bash">curl -X POST "{BASE_URL}/check_api" \
  -H "Content-Type: application/json" \
  -d '{
    "api_key": "{YOUR_API_KEY}",
    "main_product_image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
    "other_product_images": [
      "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
      "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
    ],
    "description": "Wireless bluetooth headphones with noise cancellation",
    "ip_keywords": ["bluetooth", "wireless", "headphones"],
    "product_category": "Electronics",
    "language": "en"
  }'</code></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <h3>Response</h3>
                <p>The API returns a JSON response with a unique <code>check_id</code> that you'll use to retrieve results.</p>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="json">Response</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="json">
                        <pre><code class="language-json">{
  "check_id": "1234567890123456",
  "status": "queued",
  "message": "Analysis has been queued. Use the check_status endpoint to poll for results.",
  "estimated_completion_time": 45
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- Retrieve Results Section -->
            <section id="retrieve-results" class="section">
                <h2>Retrieve Results</h2>
                <p><span class="badge get">GET</span> <code>/check_status/{check_id}</code></p>

                <p>Retrieve the analysis results for a submitted check. Since analysis can take time, you'll need to poll this endpoint until the status becomes "completed".</p>

                <h3>Request Parameters</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>check_id</code></td>
                                <td>string</td>
                                <td><span class="badge required">Required</span></td>
                                <td>The check ID returned from the submit endpoint</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Response Status Values</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Description</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>queued</code></td>
                                <td>Request is waiting in the processing queue</td>
                                <td>Poll again in 20 seconds</td>
                            </tr>
                            <tr>
                                <td><code>processing</code></td>
                                <td>Analysis is currently running</td>
                                <td>Poll again in 3 seconds</td>
                            </tr>
                            <tr>
                                <td><code>completed</code></td>
                                <td>Analysis is complete, results available</td>
                                <td>Process the results</td>
                            </tr>
                            <tr>
                                <td><code>error</code></td>
                                <td>Analysis failed due to an error</td>
                                <td>Check error details and retry if needed</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Example Request</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests
import time

def poll_for_results(check_id, base_url, max_attempts=120):
    """
    Poll for results with exponential backoff
    """
    attempt = 0

    while attempt < max_attempts:
        try:
            response = requests.get(f"{base_url}/check_status/{check_id}")

            if response.status_code == 200:
                result = response.json()
                status = result.get("status")

                print(f"Attempt {attempt + 1}: Status = {status}")

                if status == "completed":
                    print("Analysis completed!")
                    return result.get("result")

                elif status == "error":
                    print(f"Analysis failed: {result.get('message')}")
                    return None

                elif status in ["queued", "processing"]:
                    # Wait based on status
                    wait_time = 20 if status == "queued" else 3
                    print(f"Waiting {wait_time} seconds...")
                    time.sleep(wait_time)

            else:
                print(f"HTTP Error: {response.status_code}")
                break

        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            break

        attempt += 1

    print("Max attempts reached or error occurred")
    return None

# Usage example
BASE_URL = "{BASE_URL}"
check_id = "1234567890123456"  # From submit response

results = poll_for_results(check_id, BASE_URL)
if results:
    print("Final results:", results)
else:
    print("Failed to get results")</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">const BASE_URL = "{BASE_URL}";

async function pollForResults(checkId, maxAttempts = 120) {
    let attempt = 0;

    while (attempt < maxAttempts) {
        try {
            const response = await fetch(`${BASE_URL}/check_status/${checkId}`);

            if (response.ok) {
                const result = await response.json();
                const status = result.status;

                console.log(`Attempt ${attempt + 1}: Status = ${status}`);

                if (status === "completed") {
                    console.log("Analysis completed!");
                    return result.result;

                } else if (status === "error") {
                    console.log(`Analysis failed: ${result.message}`);
                    return null;

                } else if (status === "queued" || status === "processing") {
                    // Wait based on status
                    const waitTime = status === "queued" ? 20000 : 3000;
                    console.log(`Waiting ${waitTime/1000} seconds...`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                }

            } else {
                console.log(`HTTP Error: ${response.status}`);
                break;
            }

        } catch (error) {
            console.log(`Request failed: ${error}`);
            break;
        }

        attempt++;
    }

    console.log("Max attempts reached or error occurred");
    return null;
}

// Usage example
const checkId = "1234567890123456"; // From submit response

pollForResults(checkId)
    .then(results => {
        if (results) {
            console.log("Final results:", results);
        } else {
            console.log("Failed to get results");
        }
    })
    .catch(error => {
        console.error("Polling failed:", error);
    });</code></pre>
                    </div>
                </div>

                <h3>Successful Response Example</h3>
                <p>When the analysis is complete, you'll receive a comprehensive response with infringement details:</p>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="json">Response</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="json">
                        <pre><code class="language-json">{
  "status": "completed",
  "result": {
    "check_id": "1234567890123456",
    "status": "success",
    "risk_level": "High Risk",
    "results": [
      {
        "ip_type": "trademark",
        "ip_owner": "Apple Inc.",
        "risk_level": "High Risk",
        "risk_score": 8.5,
        "similarity_score": 0.92,
        "description": "Registered trademark for wireless audio devices",
        "registration_number": "US87654321",
        "registration_date": "2018-03-15",
        "ip_url": "https://example.com/trademark/87654321.jpg",
        "plaintiff_id": 12345,
        "plaintiff_name": "Apple Inc.",
        "number_of_cases": 15,
        "last_case_docket": "1:23-cv-00123",
        "last_case_date_filed": "2023-08-15",
        "legal_opinion": "High likelihood of trademark infringement due to similar design elements and product category overlap."
      },
      {
        "ip_type": "patent",
        "ip_owner": "Sony Corporation",
        "risk_level": "Medium Risk",
        "risk_score": 6.2,
        "similarity_score": 0.78,
        "description": "Noise cancellation technology patent",
        "patent_number": "US10123456",
        "patent_date": "2019-11-20",
        "ip_url": "https://example.com/patent/10123456.pdf",
        "plaintiff_id": 67890,
        "plaintiff_name": "Sony Corporation",
        "number_of_cases": 8,
        "last_case_docket": "1:22-cv-00456",
        "last_case_date_filed": "2022-12-03",
        "legal_opinion": "Moderate risk of patent infringement. Consider design modifications to avoid key claims."
      }
    ]
  }
}</code></pre>
                    </div>
                </div>

                <h3>Response Fields</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Type</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>check_id</code></td>
                                <td>string</td>
                                <td>Unique identifier for this check</td>
                            </tr>
                            <tr>
                                <td><code>status</code></td>
                                <td>string</td>
                                <td>Overall status of the analysis</td>
                            </tr>
                            <tr>
                                <td><code>risk_level</code></td>
                                <td>string</td>
                                <td>Overall risk assessment (High Risk, Medium Risk, Low Risk)</td>
                            </tr>
                            <tr>
                                <td><code>results</code></td>
                                <td>array</td>
                                <td>Array of potential IP infringements found</td>
                            </tr>
                            <tr>
                                <td><code>results[].ip_type</code></td>
                                <td>string</td>
                                <td>Type of IP (trademark, patent, copyright)</td>
                            </tr>
                            <tr>
                                <td><code>results[].ip_owner</code></td>
                                <td>string</td>
                                <td>Owner of the intellectual property</td>
                            </tr>
                            <tr>
                                <td><code>results[].risk_score</code></td>
                                <td>number</td>
                                <td>Numerical risk score (0-10)</td>
                            </tr>
                            <tr>
                                <td><code>results[].similarity_score</code></td>
                                <td>number</td>
                                <td>Visual similarity score (0-1)</td>
                            </tr>
                            <tr>
                                <td><code>results[].ip_url</code></td>
                                <td>string</td>
                                <td>URL to view the IP evidence image</td>
                            </tr>
                            <tr>
                                <td><code>results[].number_of_cases</code></td>
                                <td>number</td>
                                <td>Number of court cases involving this IP owner</td>
                            </tr>
                            <tr>
                                <td><code>results[].legal_opinion</code></td>
                                <td>string</td>
                                <td>AI-generated legal assessment</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Error Handling Section -->
            <section id="errors" class="section">
                <h2>Error Handling</h2>
                <p>The API uses standard HTTP status codes and returns detailed error information in a consistent format.</p>

                <h3>Error Response Format</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="json">Error Response</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="json">
                        <pre><code class="language-json">{
  "error": {
    "error_code": "INVALID_API_KEY",
    "message": "The provided API Key is invalid or does not exist.",
    "details": "Please check your API key and ensure it's correctly included in the request."
  }
}</code></pre>
                    </div>
                </div>

                <h3>Common Error Codes</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>HTTP Status</th>
                                <th>Error Code</th>
                                <th>Description</th>
                                <th>Solution</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>401</td>
                                <td><code>INVALID_API_KEY</code></td>
                                <td>Invalid or missing API key</td>
                                <td>Check your API key is correct and included in the request</td>
                            </tr>
                            <tr>
                                <td>400</td>
                                <td><code>MISSING_MAIN_IMAGE</code></td>
                                <td>Main product image is required</td>
                                <td>Include a main_product_image in your request</td>
                            </tr>
                            <tr>
                                <td>400</td>
                                <td><code>MISSING_REQUIRED_FIELD</code></td>
                                <td>A required field is missing</td>
                                <td>Check the error details for the specific missing field</td>
                            </tr>
                            <tr>
                                <td>429</td>
                                <td><code>RATE_LIMIT_MINUTE_EXCEEDED</code></td>
                                <td>Per-minute rate limit exceeded</td>
                                <td>Wait 60 seconds before making another request</td>
                            </tr>
                            <tr>
                                <td>429</td>
                                <td><code>RATE_LIMIT_DAILY_EXCEEDED</code></td>
                                <td>Daily rate limit exceeded</td>
                                <td>Wait until the next day or upgrade your plan</td>
                            </tr>
                            <tr>
                                <td>404</td>
                                <td><code>RESULTS_NOT_FOUND</code></td>
                                <td>Check ID not found</td>
                                <td>Verify the check_id is correct</td>
                            </tr>
                            <tr>
                                <td>500</td>
                                <td><code>SERVER_ERROR</code></td>
                                <td>Internal server error</td>
                                <td>Retry the request or contact support</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Error Handling Example</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import requests
import time

def handle_api_request(url, data, max_retries=3):
    """
    Handle API request with proper error handling and retries
    """
    for attempt in range(max_retries):
        try:
            response = requests.post(url, json=data, timeout=30)

            if response.status_code == 200:
                return response.json()

            elif response.status_code == 429:
                # Rate limit exceeded
                error_data = response.json()
                error_code = error_data.get('error', {}).get('error_code')

                if error_code == 'RATE_LIMIT_MINUTE_EXCEEDED':
                    print("Rate limit exceeded. Waiting 60 seconds...")
                    time.sleep(60)
                    continue
                elif error_code == 'RATE_LIMIT_DAILY_EXCEEDED':
                    print("Daily limit exceeded. Please try tomorrow.")
                    return None

            elif response.status_code == 401:
                print("Authentication failed. Check your API key.")
                return None

            else:
                # Other errors
                error_data = response.json()
                print(f"API Error: {error_data}")
                return None

        except requests.exceptions.Timeout:
            print(f"Request timeout (attempt {attempt + 1})")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff

        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            return None

    print("Max retries exceeded")
    return None</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">async function handleApiRequest(url, data, maxRetries = 3) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                return await response.json();

            } else if (response.status === 429) {
                // Rate limit exceeded
                const errorData = await response.json();
                const errorCode = errorData.error?.error_code;

                if (errorCode === 'RATE_LIMIT_MINUTE_EXCEEDED') {
                    console.log('Rate limit exceeded. Waiting 60 seconds...');
                    await new Promise(resolve => setTimeout(resolve, 60000));
                    continue;
                } else if (errorCode === 'RATE_LIMIT_DAILY_EXCEEDED') {
                    console.log('Daily limit exceeded. Please try tomorrow.');
                    return null;
                }

            } else if (response.status === 401) {
                console.log('Authentication failed. Check your API key.');
                return null;

            } else {
                // Other errors
                const errorData = await response.json();
                console.log('API Error:', errorData);
                return null;
            }

        } catch (error) {
            console.log(`Request failed (attempt ${attempt + 1}):`, error);
            if (attempt < maxRetries - 1) {
                // Exponential backoff
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
            }
        }
    }

    console.log('Max retries exceeded');
    return null;
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- Rate Limiting Section -->
            <section id="rate-limiting" class="section">
                <h2>Rate Limiting & Retries</h2>
                <p>The API implements rate limiting to ensure fair usage and system stability.</p>

                <h3>Rate Limits</h3>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Limit Type</th>
                                <th>Default Limit</th>
                                <th>Reset Period</th>
                                <th>Error Code</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Per-minute requests</td>
                                <td>1 request</td>
                                <td>60 seconds</td>
                                <td><code>RATE_LIMIT_MINUTE_EXCEEDED</code></td>
                            </tr>
                            <tr>
                                <td>Daily requests</td>
                                <td>100 requests</td>
                                <td>24 hours</td>
                                <td><code>RATE_LIMIT_DAILY_EXCEEDED</code></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert info">
                    <strong>Note:</strong> Rate limits may vary based on your API plan. Contact support for higher limits if needed.
                </div>

                <h3>Best Practices</h3>
                <ul>
                    <li><strong>Implement exponential backoff</strong> - Wait progressively longer between retries</li>
                    <li><strong>Handle rate limit responses</strong> - Wait for the specified time before retrying</li>
                    <li><strong>Cache results</strong> - Store completed analysis results to avoid duplicate requests</li>
                    <li><strong>Batch processing</strong> - Process multiple products sequentially rather than concurrently</li>
                    <li><strong>Monitor usage</strong> - Track your API usage to stay within limits</li>
                </ul>

                <h3>Retry Strategy Example</h3>
                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python">import time
import random

def exponential_backoff_retry(func, max_retries=5, base_delay=1):
    """
    Retry function with exponential backoff and jitter
    """
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e

            # Calculate delay with exponential backoff and jitter
            delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
            print(f"Attempt {attempt + 1} failed. Retrying in {delay:.2f} seconds...")
            time.sleep(delay)

    raise Exception("Max retries exceeded")

# Usage
def submit_check():
    # Your API call here
    response = requests.post(url, json=data)
    if response.status_code != 200:
        raise Exception(f"API call failed: {response.status_code}")
    return response.json()

try:
    result = exponential_backoff_retry(submit_check)
    print("Success:", result)
except Exception as e:
    print("Failed after retries:", e)</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">async function exponentialBackoffRetry(func, maxRetries = 5, baseDelay = 1000) {
    for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
            return await func();
        } catch (error) {
            if (attempt === maxRetries - 1) {
                throw error;
            }

            // Calculate delay with exponential backoff and jitter
            const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
            console.log(`Attempt ${attempt + 1} failed. Retrying in ${delay/1000:.2f} seconds...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    throw new Error("Max retries exceeded");
}

// Usage
async function submitCheck() {
    const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`);
    }

    return await response.json();
}

try {
    const result = await exponentialBackoffRetry(submitCheck);
    console.log("Success:", result);
} catch (error) {
    console.log("Failed after retries:", error);
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- FAQ Section -->
            <section id="faq" class="section">
                <h2>Frequently Asked Questions</h2>

                <h3>General Questions</h3>

                <h4>How long does analysis take?</h4>
                <p>Analysis typically takes 30-90 seconds, depending on the complexity of your product and current queue length. The API provides estimated completion times when you submit a request.</p>

                <h4>What image formats are supported?</h4>
                <p>We support JPEG, PNG, WebP, and GIF formats. Images should be base64 encoded or provided as accessible URLs. Maximum file size is 10MB per image.</p>

                <h4>How accurate is the IP analysis?</h4>
                <p>Our AI-powered analysis combines visual similarity detection with comprehensive IP databases. While highly accurate, results should be reviewed by legal professionals for final decisions.</p>

                <h4>Can I check multiple products at once?</h4>
                <p>Each API call analyzes one product at a time. For multiple products, submit separate requests and process them sequentially to respect rate limits.</p>

                <h3>Technical Questions</h3>

                <h4>What should I do if my request times out?</h4>
                <p>Implement proper timeout handling and retry logic. If analysis takes longer than expected, continue polling the status endpoint rather than resubmitting the request.</p>

                <h4>How do I handle base64 encoding?</h4>
                <p>Most programming languages have built-in base64 encoding functions. Ensure you encode the raw image bytes, not the data URL prefix.</p>

                <div class="code-container">
                    <div class="code-header">
                        <div class="code-tabs">
                            <button class="code-tab active" data-lang="python">Python</button>
                            <button class="code-tab" data-lang="javascript">JavaScript</button>
                        </div>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <div class="code-block active" data-lang="python">
                        <pre><code class="language-python"># Correct base64 encoding
import base64

with open("image.jpg", "rb") as f:
    image_data = base64.b64encode(f.read()).decode('utf-8')

# Use image_data in your API request</code></pre>
                    </div>
                    <div class="code-block" data-lang="javascript">
                        <pre><code class="language-javascript">// Correct base64 encoding (browser)
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            // Remove data URL prefix
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = reject;
    });
}</code></pre>
                    </div>
                </div>

                <h4>What's the difference between IP images and reference images?</h4>
                <ul>
                    <li><strong>IP images:</strong> Images of existing intellectual property that you believe might be relevant to your product</li>
                    <li><strong>Reference images:</strong> Images of similar products or designs that inspired your product</li>
                    <li><strong>Product images:</strong> Images of your actual product that you want to check</li>
                </ul>

                <h4>How do I interpret risk scores?</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Risk Score</th>
                                <th>Risk Level</th>
                                <th>Recommendation</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>8.0 - 10.0</td>
                                <td>High Risk</td>
                                <td>Strongly consider design changes or legal consultation</td>
                            </tr>
                            <tr>
                                <td>5.0 - 7.9</td>
                                <td>Medium Risk</td>
                                <td>Review carefully, consider modifications</td>
                            </tr>
                            <tr>
                                <td>0.0 - 4.9</td>
                                <td>Low Risk</td>
                                <td>Proceed with caution, monitor for changes</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>Troubleshooting</h3>

                <h4>Common Issues and Solutions</h4>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Issue</th>
                                <th>Cause</th>
                                <th>Solution</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Invalid base64 error</td>
                                <td>Incorrect encoding or data URL prefix included</td>
                                <td>Ensure proper base64 encoding without data URL prefix</td>
                            </tr>
                            <tr>
                                <td>Request timeout</td>
                                <td>Large images or network issues</td>
                                <td>Reduce image size or increase timeout values</td>
                            </tr>
                            <tr>
                                <td>Empty results</td>
                                <td>No similar IP found in database</td>
                                <td>This is normal - it means no significant matches were found</td>
                            </tr>
                            <tr>
                                <td>Rate limit errors</td>
                                <td>Too many requests in short time</td>
                                <td>Implement proper delays and retry logic</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="alert success">
                    <strong>Need Help?</strong> If you encounter issues not covered here, please contact our support team with your check ID and error details.
                </div>
            </section>
        </main>
    </div>

    <!-- Prism.js for syntax highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

    <script>
        // Theme toggle functionality
        function toggleTheme() {
            const body = document.body;
            const themeToggle = document.querySelector('.theme-toggle');
            const darkTheme = document.getElementById('prism-dark-theme');

            if (body.getAttribute('data-theme') === 'dark') {
                body.removeAttribute('data-theme');
                themeToggle.textContent = '🌙';
                darkTheme.disabled = true;
                localStorage.setItem('theme', 'light');
            } else {
                body.setAttribute('data-theme', 'dark');
                themeToggle.textContent = '☀️';
                darkTheme.disabled = false;
                localStorage.setItem('theme', 'dark');
            }
        }

        // Initialize theme from localStorage
        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme');
            const themeToggle = document.querySelector('.theme-toggle');
            const darkTheme = document.getElementById('prism-dark-theme');

            if (savedTheme === 'dark') {
                document.body.setAttribute('data-theme', 'dark');
                themeToggle.textContent = '☀️';
                darkTheme.disabled = false;
            }
        }

        // Navigation functionality
        function initializeNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            const sections = document.querySelectorAll('.section');

            // Handle navigation clicks
            navItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const targetId = item.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);

                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                        updateActiveNavItem(item);
                    }
                });
            });

            // Handle scroll spy
            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.pageYOffset >= sectionTop - 100) {
                        current = section.getAttribute('id');
                    }
                });

                navItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.getAttribute('href') === `#${current}`) {
                        item.classList.add('active');
                    }
                });
            });
        }

        function updateActiveNavItem(activeItem) {
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            activeItem.classList.add('active');
        }

        // Code tab functionality
        function initializeCodeTabs() {
            const codeContainers = document.querySelectorAll('.code-container');

            codeContainers.forEach(container => {
                const tabs = container.querySelectorAll('.code-tab');
                const blocks = container.querySelectorAll('.code-block');

                tabs.forEach(tab => {
                    tab.addEventListener('click', () => {
                        const lang = tab.getAttribute('data-lang');

                        // Update active tab
                        tabs.forEach(t => t.classList.remove('active'));
                        tab.classList.add('active');

                        // Update active code block
                        blocks.forEach(block => {
                            block.classList.remove('active');
                            if (block.getAttribute('data-lang') === lang) {
                                block.classList.add('active');
                            }
                        });
                    });
                });
            });
        }

        // Copy code functionality
        function copyCode(button) {
            const container = button.closest('.code-container');
            const activeBlock = container.querySelector('.code-block.active');
            const code = activeBlock.querySelector('code');

            if (code) {
                const text = code.textContent;
                navigator.clipboard.writeText(text).then(() => {
                    const originalText = button.textContent;
                    button.textContent = 'Copied!';
                    button.classList.add('copied');

                    setTimeout(() => {
                        button.textContent = originalText;
                        button.classList.remove('copied');
                    }, 2000);
                }).catch(err => {
                    console.error('Failed to copy code:', err);
                });
            }
        }

        // Disclosure functionality
        function toggleDisclosure(header) {
            const disclosure = header.parentElement;
            const arrow = header.querySelector('.disclosure-arrow');

            disclosure.classList.toggle('open');

            if (disclosure.classList.contains('open')) {
                arrow.textContent = '▼';
            } else {
                arrow.textContent = '▶';
            }
        }

        // Global language toggle functionality
        function initializeGlobalLanguageToggle() {
            // This would be implemented if you want a global Python/JS toggle
            // For now, each code block has its own toggle
        }

        // Responsive sidebar toggle (for mobile)
        function initializeMobileMenu() {
            // Add mobile menu button if needed
            if (window.innerWidth <= 768) {
                // Mobile-specific functionality can be added here
            }
        }

        // Initialize all functionality when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            initializeTheme();
            initializeNavigation();
            initializeCodeTabs();
            initializeGlobalLanguageToggle();
            initializeMobileMenu();
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            initializeMobileMenu();
        });
    </script>

    <style>
        /* Additional responsive styles */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                max-width: 100vw;
                padding: 20px;
            }

            .content-header h1 {
                font-size: 24px;
            }

            .content-header p {
                font-size: 16px;
            }

            .code-container {
                margin: 16px -20px;
                border-radius: 0;
            }

            .table-container {
                margin: 16px -20px;
                border-radius: 0;
            }
        }

        /* Disclosure styles */
        .disclosure {
            margin: 16px 0;
        }

        .disclosure-header {
            background: var(--background-light);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s ease;
        }

        .disclosure-header:hover {
            background: var(--background-white);
        }

        .disclosure-content {
            display: none;
            margin-top: 8px;
        }

        .disclosure.open .disclosure-content {
            display: block;
        }

        .disclosure-arrow {
            transition: transform 0.2s ease;
            font-family: monospace;
        }

        /* Smooth scrolling for anchor links */
        html {
            scroll-behavior: smooth;
        }

        /* Focus styles for accessibility */
        .nav-item:focus,
        .code-tab:focus,
        .copy-button:focus,
        .theme-toggle:focus,
        .disclosure-header:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }
    </style>
</body>
</html>
