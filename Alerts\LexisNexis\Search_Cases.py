from langfuse import observe
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException
import time
import pandas as pd
from difflib import SequenceMatcher
from datetime import date, datetime
import traceback
from ..Chrome_Driver import  move_mouse_to, send_keyboard_input, random_delay
import logging

def lexis_search_cases_by_date(driver, case_date, case_end_date=None):
    try: 
        date_str = clean_date(case_date)
        if case_end_date:
            case_end_date_str = clean_date(case_end_date)

        driver.get('https://advance.lexis.com/courtlinksearch')  # return to search page

        # Ensure the browser window is in focus
        driver.switch_to.window(driver.current_window_handle)

        ## Input the defendant
        # Locate the "Litigant" section
        litigant_section = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, "//h2[text()='Litigant']/ancestor::fieldset")))

        # Locate the input box within the "Litigant" section
        input_box = litigant_section.find_element(By.XPATH, ".//input[@type='text' and @placeholder='Enter Name...']")

        # Clear the input box and enter "Schedule"
        input_box.clear()
        send_keyboard_input(driver, input_box, "Schedule")

        # Locate and uncheck the "Plaintiff" checkbox if it's selected
        plaintiff_checkbox = litigant_section.find_element(By.ID, "litigant-plaintiff1")
        if plaintiff_checkbox.is_selected():
            move_mouse_to(driver, plaintiff_checkbox)
            plaintiff_checkbox.click()

        # Locate and uncheck the "Other" checkbox if it's selected
        other_checkbox = litigant_section.find_element(By.ID, "litigant-other1")
        if other_checkbox.is_selected():
            move_mouse_to(driver, other_checkbox)
            other_checkbox.click()


        ## Input the date
        # Locate the "Date Filed" section
        date_filed_section = driver.find_element(By.XPATH, "//h2[text()='Date Filed']/ancestor::dateselector")

        # Locate the select element within the "Date Filed" section
        select_element = date_filed_section.find_element(By.TAG_NAME, "select")

        # Create a Select object to interact with the dropdown
        select_obj = Select(select_element)

        if case_end_date:
            # Select "Date between" option by visible text
            select_obj.select_by_visible_text("Date is between")
        else: 
            select_obj.select_by_visible_text("Date is")

        # Locate the input boxes within the "Date Filed" section
        date_inputs = date_filed_section.find_elements(By.XPATH, ".//input[@type='text' and @aria-label='Enter the date']")
        start_input_box = date_inputs[0]
        driver.execute_script("arguments[0].scrollIntoView(); window.scrollBy(0, -150);", start_input_box) # puts the input_box in view

        # Clear the input box and enter the date
        start_input_box.send_keys(Keys.CONTROL + 'a')  # Use Keys.COMMAND if on Mac
        start_input_box.send_keys(Keys.DELETE)
        send_keyboard_input(driver, start_input_box, date_str)

        if case_end_date:
            # Handle end date (second input)
            end_input = date_inputs[1]
            end_input.send_keys(Keys.CONTROL + 'a')
            end_input.send_keys(Keys.DELETE)
            send_keyboard_input(driver, end_input, case_end_date_str)

        # Click the search button
        search_button = driver.find_element(By.ID, 'triggersearch')
        move_mouse_to(driver, search_button)
        search_button.click()
        random_delay()
        print(f"Search button clicked for {date}")
    except Exception as e:
        print(f"Error in lexis_search_cases_by_date: {e}")
        print(f"Traceback:\n{traceback.format_exc()}")


@observe(capture_input=False, capture_output=False)
def lexis_search_a_case_by_docket(driver, docket_number, court, title, use_schedule=False):
    driver.get('https://advance.lexis.com/courtlinksearch')  # return to search page

    try:
        # Ensure the browser window is in focus
        driver.switch_to.window(driver.current_window_handle)
        
        # Wait for the form to be populated by JavaScript
        WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.ID, 'docketNumberInput')))

        # Put the docket number in the 'docketNumberInput' field
        if "-cv-" in docket_number:
            docket_number = docket_number.replace("-cv-", "cv")
        while "cv0" in docket_number:
            docket_number = docket_number.replace("cv0", "cv")
   
        docket_number_input = driver.find_element(By.ID, 'docketNumberInput')
        send_keyboard_input(driver, docket_number_input, docket_number)


        if court and court != "":
            # Click on the court list selection container
            court_list_container = driver.find_element(By.CSS_SELECTOR, '.courtlistselection-container .dropdownContainer')
            move_mouse_to(driver, court_list_container)
            court_list_container.click()

            WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.ID, 'courtsdropdown')))
            court_search = driver.find_element(By.ID, 'courtsdropdown')
            # send_keyboard_input(driver, court_search, court)
            court_search.send_keys(court)
            random_delay()

            # Select the first "input" in the HTML code and tick it
            first_court_checkbox = driver.find_element(By.CSS_SELECTOR, '.courtsContainer .inputContainer .courtCheckbox')
            move_mouse_to(driver, first_court_checkbox)
            first_court_checkbox.click()

        if use_schedule:
            ## Input the defendant
            # Locate the "Litigant" section
            litigant_section = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, "//h2[text()='Litigant']/ancestor::fieldset")))

            # Locate the input box within the "Litigant" section
            input_box = litigant_section.find_element(By.XPATH, ".//input[@type='text' and @placeholder='Enter Name...']")

            # Clear the input box and enter "Schedule"
            input_box.clear()
            send_keyboard_input(driver, input_box, "Schedule")

            # Locate and uncheck the "Plaintiff" checkbox if it's selected
            plaintiff_checkbox = litigant_section.find_element(By.ID, "litigant-plaintiff1")
            if plaintiff_checkbox.is_selected():
                move_mouse_to(driver, plaintiff_checkbox)
                plaintiff_checkbox.click()

            # Locate and uncheck the "Other" checkbox if it's selected
            other_checkbox = litigant_section.find_element(By.ID, "litigant-other1")
            if other_checkbox.is_selected():
                move_mouse_to(driver, other_checkbox)
                other_checkbox.click()
            

        # Click the search button
        search_button = driver.find_element(By.ID, 'triggersearch')
        move_mouse_to(driver, search_button)
        search_button.click()
        random_delay()
        print(f"Search button clicked for {docket_number}")


        # Wait for the elements to be present and stable
        try:
            results = wait_for_results_to_stabilize(driver)
            print(f"Search results obtained for {docket_number}")

            def score_link(element):
                link_text = element.find_element(By.CSS_SELECTOR, 'h2.doc-title a').text.strip()
                if title and title != "":
                    score = SequenceMatcher(None, link_text, title).ratio()
                else:
                    score = 0
                score = score + 1 if "schedule" in link_text.lower() else score
                return score

            best_match = max(results, key=score_link, default=None)
            print(f"Best match found for {docket_number}")

            if best_match:
                link_element = best_match.find_element(By.CSS_SELECTOR, 'h2.doc-title a')
                move_mouse_to(driver, link_element)
                actions = ActionChains(driver)
                actions.key_down(Keys.CONTROL).click(link_element).key_up(Keys.CONTROL).perform()  # on a new tab
                print(f"Case {docket_number} clicked")
                return True
            else:
                raise ValueError(f"No matching case found in results list for {docket_number}")  # Raise exception

        except Exception: # Catch other exceptions
            try:
                header_div = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "Header")))
                print(f"Directly to case page for {docket_number}")
                return True  # We are on the case page
            except Exception:
                if driver.find_element(By.CLASS_NAME, 'errorHeading'):
                    if 'Search Limit Exceeded' in driver.find_element(By.CLASS_NAME, 'errorHeading').text:
                        print(f"Search Limit Exceeded.")
                        exit()
                    else:
                        print(f"Error: {driver.find_element(By.CLASS_NAME, 'errorHeading').text}")
                else:
                    print(f"Case {docket_number} not found")
                return False

    except Exception as e:
        print(f"Error finding case for {docket_number} in {court}: {e}")
        print(f"Traceback:\n{traceback.format_exc()}")


def wait_for_results_to_stabilize(driver, timeout=10, poll_frequency=1):
    try:
        # 1. A more robust wait: wait for the loading indicator to disappear.
        # This is often more reliable than waiting for results to appear, as it
        # confirms the loading process has finished.
        WebDriverWait(driver, timeout).until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, 'div.loadbox'))
        )
    except Exception:
        # If the loadbox doesn't disappear, we can still proceed,
        # but we'll print a warning. The old logic might still catch the results.
        print("Warning: The main loading indicator did not disappear within the timeout.")
    
    result_item_selector = 'result-item[data-id^="sr"]'
    # Wait until at least one result item is present before starting stabilization check
    try:
        WebDriverWait(driver, 5).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, result_item_selector))
        )
    except Exception:
        print("No result items found after waiting for loadbox.")
        return []

    end_time = time.time() + timeout
    previous_count = -1  # Start with -1 to ensure the loop runs at least once

    while time.time() < end_time:
        elements = driver.find_elements(By.CSS_SELECTOR, result_item_selector)
        current_count = len(elements)

        # We need at least one result, and the count must be stable.
        if current_count > 0 and current_count == previous_count:
            print(f"Results stabilized with {current_count} items.")
            return elements

        previous_count = current_count
        print(f"Polling... found {current_count} results so far.")
        time.sleep(poll_frequency)

    print("Timeout reached while waiting for results to stabilize.")
    # Return whatever was found last, or empty list
    final_elements = driver.find_elements(By.CSS_SELECTOR, result_item_selector)
    return final_elements

# --- Helper function for pagination (provided by user) ---
def go_to_next_page(driver): 
    # Wait for the pagination component to be present 
    wait = WebDriverWait(driver, 10)  
    try: 
        pagination_component = wait.until(EC.presence_of_element_located((By.TAG_NAME, "pagination-component"))) 
        # Try to find the "Next" button that is not disabled
        next_button = pagination_component.find_element(By.XPATH, ".//button[@name='arrow' and @aria-label='Next page' and not(@disabled)]") 
        move_mouse_to(driver, next_button) 
        next_button.click() 
        random_delay() 
        return True 
    except NoSuchElementException: 
        logging.info("No 'Next' page button found (NoSuchElementException). Likely end of results.") 
        return False 
    except TimeoutException: 
        logging.info("No 'Next' page button found (TimeoutException). Likely end of results.") 
        return False 
    except Exception as e: # General catch for other potential issues like stale element 
        logging.error(f"An unexpected error occurred trying to click 'Next' page: {e}") 
        # Attempt to re-locate and click if it was a stale element or similar transient issue 
        try: 
            # Re-find elements within the current scope of the driver 
            pagination_component_retry = driver.find_element(By.TAG_NAME, "pagination-component") 
            next_button_retry = pagination_component_retry.find_element(By.XPATH, ".//button[@name='arrow' and @aria-label='Next page' and not(@disabled)]") 
            if next_button_retry.is_displayed() and next_button_retry.is_enabled(): 
                logging.info("Retrying click on 'Next' button after error.") 
                move_mouse_to(driver, next_button_retry) 
                next_button_retry.click() 
                random_delay() 
                return True 
        except Exception as retry_e: 
            logging.error(f"Retry clicking 'Next' button failed: {retry_e}") 
        return False
    
    
def clean_date(case_date):
    # Ensure the date_parameter is a string in 'mm/dd/yyyy' format
    # If date_parameter is a datetime object, format it accordingly
    if isinstance(case_date, str):
        date_str = pd.to_datetime(case_date).strftime('%m/%d/%Y')
    elif isinstance(case_date, datetime):
        date_str = case_date.strftime('%m/%d/%Y')
    elif isinstance(case_date, date):
        date_str = case_date.strftime('%m/%d/%Y')
    else:
        raise ValueError("Invalid date format. Please provide a date in 'mm/dd/yyyy' format or as a datetime object.")

    return date_str
