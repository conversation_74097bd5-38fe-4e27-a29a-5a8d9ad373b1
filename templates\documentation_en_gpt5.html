<!doctype html>
<html lang="en" data-theme="auto">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1"/>
  <title>IP Infringement Check API · Documentation</title>

  <!-- Prism CSS (syntax highlighting) -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css"/>

  <style>
    :root{
      --bg: #ffffff;
      --fg: #1f1f1f;
      --muted: #5f6368;
      --border: #e6e6e6;
      --accent: #1a73e8; /* Google-ish blue */
      --code-bg: #f6f8fa;
      --shadow: 0 1px 2px rgba(0,0,0,.06), 0 8px 24px rgba(0,0,0,.06);
      --sidebar-bg: #fafafa;
      --kbd-bg:#f1f3f4;
    }
    @media (prefers-color-scheme: dark){
      :root{
        --bg: #0f1113;
        --fg: #e6e6e6;
        --muted:#9aa0a6;
        --border:#2a2e33;
        --accent:#8ab4f8;
        --code-bg:#15181b;
        --sidebar-bg:#121417;
        --kbd-bg:#1f2328;
      }
    }
    html[data-theme="light"]{
      --bg: #ffffff; --fg:#1f1f1f; --muted:#5f6368; --border:#e6e6e6; --accent:#1a73e8; --code-bg:#f6f8fa; --sidebar-bg:#fafafa; --kbd-bg:#f1f3f4;
    }
    html[data-theme="dark"]{
      --bg: #0f1113; --fg:#e6e6e6; --muted:#9aa0a6; --border:#2a2e33; --accent:#8ab4f8; --code-bg:#15181b; --sidebar-bg:#121417; --kbd-bg:#1f2328;
    }

    * { box-sizing: border-box; }
    body{
      margin:0; font: 16px/1.6 system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji","Segoe UI Emoji";
      background:var(--bg); color:var(--fg);
    }

    /* Layout */
    .wrap{
      display:grid; grid-template-columns: 280px 1fr; gap:32px; max-width:1200px; margin:0 auto; padding:24px;
    }
    aside{
      position:sticky; top:16px; align-self:start;
      background:var(--sidebar-bg); border:1px solid var(--border); border-radius:12px; padding:16px;
      height: calc(100vh - 32px); overflow:auto;
    }
    main{ max-width: 920px; }
    header.page{
      position: sticky; top:0; z-index:10; backdrop-filter: blur(6px);
      background: color-mix(in srgb, var(--bg) 90%, transparent);
      border-bottom: 1px solid var(--border); padding: 10px 24px;
    }
    header .row{ display:flex; align-items:center; gap:12px; justify-content:space-between; max-width:1200px; margin:0 auto; }
    .brand{ display:flex; align-items:center; gap:10px; }
    .dot{ width:10px; height:10px; background:var(--accent); border-radius:50%; box-shadow:0 0 0 3px color-mix(in srgb, var(--accent), transparent 70%); }
    .title{ font-weight:600; letter-spacing:.2px; }

    /* Nav */
    nav a{
      display:block; color:var(--fg); text-decoration:none; padding:6px 8px; border-radius:8px;
    }
    nav a:hover{ background:color-mix(in srgb, var(--accent) 12%, transparent); }
    nav a.active{ background: color-mix(in srgb, var(--accent) 18%, transparent); color: var(--fg); }
    nav .small{ color:var(--muted); font-size:12px; margin: 8px 8px 12px; text-transform: uppercase; letter-spacing:.08em; }

    /* Controls row */
    .controls{ display:flex; gap:8px; align-items:center; }
    .segmented{
      display:inline-flex; border:1px solid var(--border); border-radius:10px; overflow:hidden; background:var(--bg);
    }
    .segmented button{
      border:0; background:transparent; padding:6px 12px; color:var(--fg); cursor:pointer; font-weight:500;
    }
    .segmented button[aria-pressed="true"]{ background:var(--accent); color:#fff; }
    .btn{
      border:1px solid var(--border); background:var(--bg); color:var(--fg); border-radius:10px; padding:6px 10px; cursor:pointer;
    }
    .btn:hover{ box-shadow: var(--shadow); }
    .icon{ width:18px; height:18px; display:inline-block; }

    /* Sections */
    section{ padding: 28px 0; border-bottom:1px solid var(--border); }
    h1, h2, h3{ line-height:1.25; }
    h1{ font-size: 28px; margin: 0 0 10px; }
    h2{ font-size: 22px; margin: 24px 0 8px; position:relative; }
    h3{ font-size: 18px; margin: 18px 0 8px; }
    p, ul, ol, .callout{ color: var(--fg); }
    .muted{ color: var(--muted); }
    .callout{
      border:1px solid var(--border); border-left:4px solid var(--accent); padding:12px 14px; border-radius:10px; background:color-mix(in srgb, var(--accent) 6%, var(--bg));
    }
    .grid{
      display:grid; gap:18px;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    kbd{
      background: var(--kbd-bg); padding: 2px 6px; border-radius:6px; border:1px solid var(--border); font-size: 90%;
    }

    /* Code */
    pre{
      background: var(--code-bg) !important; border:1px solid var(--border); border-radius:12px; padding:12px 12px 10px; position:relative;
      overflow:auto; box-shadow: var(--shadow);
    }
    code{ font-family: ui-monospace,SFMono-Regular,Consolas,"Liberation Mono",Menlo,monospace; }
    .codebox{ position: relative; }
    .copy{
      position:absolute; top:8px; right:8px; border:1px solid var(--border);
      background:var(--bg); color:var(--fg); border-radius:8px; padding:4px 8px; cursor:pointer; font-size:12px;
    }
    .langchip{
      position:absolute; top:8px; left:8px; font-size:12px; color:var(--muted);
      background:transparent; border:0;
    }
    .code-sample[data-lang]{ display:none; }
    .code-sample[data-lang].show{ display:block; }

    /* Details */
    details{
      border:1px dashed var(--border); border-radius:10px; padding:10px 12px; background: color-mix(in srgb, var(--accent) 4%, var(--bg));
    }
    details>summary{ cursor:pointer; font-weight:600; }
    .tag{ display:inline-block; padding:2px 8px; border:1px solid var(--border); border-radius:999px; font-size:12px; color:var(--muted); }

    /* Anchor link on hover */
    .anchor{
      opacity:0; text-decoration:none; margin-left:.4rem; color:var(--muted);
    }
    h2:hover .anchor, h3:hover .anchor{ opacity:1; }

    /* Skip link */
    .skip{ position:absolute; left:-9999px; top:auto; width:1px; height:1px; overflow:hidden; }
    .skip:focus{ position:static; width:auto; height:auto; }

    /* Footer */
    footer{ color:var(--muted); padding:40px 0; }
  </style>
</head>
<body>
  <a href="#content" class="skip">Skip to content</a>

  <header class="page" role="banner" aria-label="Top bar">
    <div class="row">
      <div class="brand"><span class="dot" aria-hidden="true"></span><span class="title">IP Infringement Check API</span></div>
      <div class="controls">
        <!-- Global language toggle -->
        <div class="segmented" role="group" aria-label="Code language toggle">
          <button id="btnPy" aria-pressed="true" title="Show Python">Python</button>
          <button id="btnJs" aria-pressed="false" title="Show JavaScript">JS</button>
        </div>

        <!-- Theme toggle -->
        <button id="themeToggle" class="btn" aria-label="Toggle color theme" title="Toggle color theme">
          <span class="icon" aria-hidden="true">🌓</span>
        </button>
      </div>
    </div>
  </header>

  <div class="wrap">
    <aside aria-label="Sidebar Navigation">
      <nav id="toc" aria-label="On-page navigation">
        <div class="small">Docs</div>
        <a href="#overview">Overview</a>
        <a href="#auth">Authentication</a>
        <a href="#submit">Submit a product for IP check</a>
        <a href="#result">Retrieve results</a>
        <a href="#errors">Errors</a>
        <a href="#rate">Rate limiting & retries</a>
        <a href="#faq">FAQ</a>
      </nav>
    </aside>

    <main id="content" role="main">
      <section id="overview">
        <h1>Overview</h1>
        <p class="muted">Clean, minimal, and production-ready API documentation. All examples below are runnable with placeholders.</p>

        <div class="callout" role="note" aria-label="API overview">
          <p><strong>This API service helps ecommerce sellers</strong> quickly check whether their products may infringe on the intellectual property (IP) of another party — covering copyrights, trademarks, and patents.</p>
          <p><strong>Sellers can submit:</strong></p>
          <ul>
            <li>Product images</li>
            <li>Any image of intellectual property used in making the product</li>
            <li>Reference images (e.g., a similar product that inspired the design)</li>
            <li>Product description</li>
            <li>Relevant keywords</li>
          </ul>
          <p><strong>The API then returns:</strong></p>
          <ul>
            <li>The most likely infringements</li>
            <li>Images of the infringed IP</li>
            <li>Information on whether that IP has been used in court cases</li>
            <li>A legal opinion report assessing the severity of potential infringement</li>
          </ul>
          <p>This enables sellers to make informed decisions before listing or selling their products.</p>
        </div>

        <div class="grid" aria-label="Spec quick facts">
          <div>
            <h3>Base URL</h3>
            <p><code>{BASE_URL}</code></p>
          </div>
          <div>
            <h3>Auth</h3>
            <p>API Key via header:<br/><code>Authorization: Bearer {YOUR_API_KEY}</code></p>
          </div>
          <div>
            <h3>Content types</h3>
            <p><span class="tag">multipart/form-data</span> for file uploads<br/>
               <span class="tag">application/json</span> (alternative; URLs or base64)</p>
          </div>
        </div>
      </section>

      <section id="auth">
        <h2>Authentication <a class="anchor" href="#auth" aria-label="Anchor to Authentication">#</a></h2>
        <p>Send your API key with every request:</p>
        <pre class="codebox"><button class="copy" aria-label="Copy to clipboard">Copy</button><code class="language-bash">Authorization: Bearer {YOUR_API_KEY}</code></pre>
        <p class="muted">If the key is invalid or missing, the API returns <code>401 Unauthorized</code> with a structured error (see <a href="#errors">Errors</a>).</p>
      </section>

      <section id="submit">
        <h2>Submit a product for IP check <a class="anchor" href="#submit">#</a></h2>
        <p><strong>POST</strong> <code>/check</code></p>
        <p>Upload one main product image (recommended) plus any additional images and context. Use <span class="tag">multipart/form-data</span>.</p>

        <h3>Request body (multipart)</h3>
        <ul>
          <li><code>description</code> (text) — Product description</li>
          <li><code>keywords[]</code> (text, repeatable) — Relevant keywords</li>
          <li><code>main_product_image</code> (file, image/*)</li>
          <li><code>other_product_images[]</code> (files, image/*, optional)</li>
          <li><code>ip_images[]</code> (files, image/*, optional) — Images of the IP used in making the product (if any)</li>
          <li><code>reference_images[]</code> (files, image/*, optional) — Reference / inspiration images</li>
        </ul>

        <div class="callout">
          <strong>Alternative JSON input:</strong> You may submit JSON with URLs or base64-encoded images instead of files. Use fields:
          <code>main_product_image</code> (URL/base64), <code>other_product_images[]</code> (URL/base64), <code>ip_images[]</code>, <code>reference_images[]</code>, <code>description</code>, <code>keywords[]</code>.
        </div>

        <h3>Response</h3>
        <p>Returns a unique <code>check_id</code>. Results are processed asynchronously; use <a href="#result">Retrieve results</a> to poll.</p>
        <pre class="codebox"><button class="copy">Copy</button><code class="language-json">{
  "check_id": "{CHECK_ID}",
  "status": "queued",
  "estimated_completion_time": 60
}</code></pre>

        <h3>Example — Python (requests)</h3>
        <div class="code-sample codebox" data-lang="python">
<button class="copy">Copy</button><button class="langchip" disabled>Python</button>
<pre><code class="language-python">import requests

BASE_URL = "{BASE_URL}"
API_KEY = "{YOUR_API_KEY}"

files = {
    "main_product_image": ("main.jpg", open("main.jpg", "rb"), "image/jpeg"),
}
# Repeatable file fields may be provided as a list of tuples:
other_files = [
    ("other_product_images[]", ("alt1.jpg", open("alt1.jpg","rb"), "image/jpeg")),
    ("other_product_images[]", ("alt2.jpg", open("alt2.jpg","rb"), "image/jpeg")),
]
ip_files = [
    ("ip_images[]", ("ip1.png", open("ip1.png","rb"), "image/png"))
]
ref_files = [
    ("reference_images[]", ("ref1.jpg", open("ref1.jpg","rb"), "image/jpeg"))
]

data = [
    ("description", "Handmade figurine with star and mouse motif"),
    ("keywords[]", "mouse"),
    ("keywords[]", "star"),
    ("keywords[]", "figurine"),
]

headers = {"Authorization": f"Bearer {API_KEY}"}
resp = requests.post(f"{BASE_URL}/check",
                     headers=headers,
                     files=[*files.items(), *other_files, *ip_files, *ref_files],
                     data=data,
                     timeout=300)
resp.raise_for_status()
print(resp.json())</code></pre>
        </div>

        <h3>Example — JavaScript (fetch)</h3>
        <div class="code-sample codebox" data-lang="js">
<button class="copy">Copy</button><button class="langchip" disabled>JavaScript</button>
<pre><code class="language-javascript">const BASE_URL = "{BASE_URL}";
const API_KEY = "{YOUR_API_KEY}";

async function submitCheck() {
  const fd = new FormData();
  fd.append("description", "Handmade figurine with star and mouse motif");
  ["mouse", "star", "figurine"].forEach(k =&gt; fd.append("keywords[]", k));

  // &lt;input type="file" id="main" /&gt; etc.
  const main = document.getElementById("main").files[0];
  fd.append("main_product_image", main, main.name);

  const others = document.getElementById("others").files;
  for (const f of others) fd.append("other_product_images[]", f, f.name);

  const ipImgs = document.getElementById("ipimgs").files;
  for (const f of ipImgs) fd.append("ip_images[]", f, f.name);

  const refs = document.getElementById("refs").files;
  for (const f of refs) fd.append("reference_images[]", f, f.name);

  const resp = await fetch(`${BASE_URL}/check`, {
    method: "POST",
    headers: { "Authorization": `Bearer ${API_KEY}` },
    body: fd,
  });
  if (!resp.ok) throw new Error(`${resp.status} ${resp.statusText}`);
  const data = await resp.json();
  console.log(data);
  return data.check_id;
}</code></pre>
        </div>

        <details>
          <summary>cURL</summary>
          <pre class="codebox"><button class="copy">Copy</button><code class="language-bash">curl -X POST "{BASE_URL}/check" \
 -H "Authorization: Bearer {YOUR_API_KEY}" \
 -F "description=Handmade figurine with star and mouse motif" \
 -F "keywords[]=mouse" -F "keywords[]=star" -F "keywords[]=figurine" \
 -F "main_product_image=@main.jpg" \
 -F "other_product_images[]=@alt1.jpg" \
 -F "ip_images[]=@ip1.png"</code></pre>
        </details>
      </section>

      <section id="result">
        <h2>Retrieve results <a class="anchor" href="#result">#</a></h2>
        <p><strong>GET</strong> <code>/result/{check_id}</code></p>

        <p><strong>Polling behavior.</strong> This endpoint may return:</p>
        <ul>
          <li><code>200 OK</code> — Results ready (see schema below)</li>
          <li><code>202 Accepted</code> — <code>{"status":"queued" | "processing", "estimated_completion_time":&lt;sec&gt;}</code></li>
          <li><code>404 Not Found</code> — Unknown <code>check_id</code></li>
        </ul>

        <h3>Results schema</h3>
        <pre class="codebox"><button class="copy">Copy</button><code class="language-json">{
  "check_id": "{CHECK_ID}",
  "status": "success",
  "risk_level": "High Risk | Medium Risk | Low Risk",
  "results": [
    {
      "ip_type": "trademark | copyright | patent",
      "ip_owner": "Acme Studios",
      "risk_level": "High Risk",
      "risk_score": 0.92,
      "matched_ip_image": "https://.../ip.jpg",
      "number_of_cases": 12,
      "last_case_docket": "1:23-cv-12345",
      "last_case_date_filed": "2024-06-01",
      "court_history": "...",
      "legal_opinion": "Likely infringement due to ...",
      "similarity_score": 0.88
    }
  ]
}</code></pre>

        <h3>Example — Python (poll with exponential backoff)</h3>
        <div class="code-sample codebox" data-lang="python">
<button class="copy">Copy</button><button class="langchip" disabled>Python</button>
<pre><code class="language-python">import time, requests, math

BASE_URL = "{BASE_URL}"
API_KEY = "{YOUR_API_KEY}"
CHECK_ID = "{CHECK_ID}"  # from POST /check

def get_result(check_id):
    url = f"{BASE_URL}/result/{check_id}"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    return requests.get(url, headers=headers, timeout=60)

attempt = 0
while True:
    r = get_result(CHECK_ID)
    if r.status_code == 200:
        print("✅ Done:", r.json())
        break
    elif r.status_code == 202:
        payload = r.json()
        print("⏳", payload.get("status"), "ETA:", payload.get("estimated_completion_time"), "s")
        # exponential backoff w/ jitter
        delay = min(30, 1 * (2 ** attempt)) + (0.25 * math.sin(attempt))
        time.sleep(delay)
        attempt += 1
    elif r.status_code == 404:
        raise SystemExit("Check ID not found")
    elif r.status_code == 401:
        raise SystemExit("Unauthorized: invalid API key")
    elif r.status_code == 429:
        # retry after (if provided) or backoff
        wait = int(r.headers.get("Retry-After", 10))
        time.sleep(wait)
    else:
        r.raise_for_status()</code></pre>
        </div>

        <h3>Example — JavaScript (poll with exponential backoff)</h3>
        <div class="code-sample codebox" data-lang="js">
<button class="copy">Copy</button><button class="langchip" disabled>JavaScript</button>
<pre><code class="language-javascript">const BASE_URL = "{BASE_URL}";
const API_KEY = "{YOUR_API_KEY}";
const CHECK_ID = "{CHECK_ID}"; // from POST /check

async function getResult(id){
  const resp = await fetch(`${BASE_URL}/result/${id}`, {
    headers: { "Authorization": `Bearer ${API_KEY}` }
  });
  return resp;
}

async function poll() {
  let attempt = 0;
  while (true) {
    const r = await getResult(CHECK_ID);
    if (r.status === 200) {
      console.log("✅ Done:", await r.json());
      break;
    } else if (r.status === 202) {
      const payload = await r.json();
      console.log("⏳", payload.status, "ETA:", payload.estimated_completion_time, "s");
      const delay = Math.min(30000, 1000 * (2 ** attempt)) + (250 * Math.random());
      await new Promise(res =&gt; setTimeout(res, delay));
      attempt++;
    } else if (r.status === 404) {
      throw new Error("Check ID not found");
    } else if (r.status === 401) {
      throw new Error("Unauthorized: invalid API key");
    } else if (r.status === 429) {
      const retryAfter = parseInt(r.headers.get("Retry-After") || "10000", 10);
      await new Promise(res =&gt; setTimeout(res, retryAfter));
    } else {
      throw new Error(`${r.status} ${r.statusText}`);
    }
  }
}

poll().catch(console.error);</code></pre>
        </div>

        <details>
          <summary>cURL</summary>
          <pre class="codebox"><button class="copy">Copy</button><code class="language-bash">curl -X GET "{BASE_URL}/result/{CHECK_ID}" \
 -H "Authorization: Bearer {YOUR_API_KEY}"</code></pre>
        </details>
      </section>

      <section id="errors">
        <h2>Errors <a class="anchor" href="#errors">#</a></h2>
        <p>All errors follow a canonical schema:</p>
        <pre class="codebox"><button class="copy">Copy</button><code class="language-json">{
  "error": {
    "error_code": "INVALID_API_KEY",
    "message": "The provided API Key is invalid or does not exist.",
    "details": "Optional, extra context"
  }
}</code></pre>
        <p>Common cases:</p>
        <ul>
          <li><strong>401 Unauthorized</strong> → <code>INVALID_API_KEY</code> (“Invalid or missing API key”)</li>
          <li><strong>404 Not Found</strong> → <code>RESULTS_NOT_FOUND</code> (“Check ID not found”)</li>
          <li><strong>429 Too Many Requests</strong> → <code>RATE_LIMIT_MINUTE_EXCEEDED</code> or <code>RATE_LIMIT_DAILY_EXCEEDED</code>; retry after a delay</li>
        </ul>
        <p class="muted">The API may also return <code>202 Accepted</code> while jobs are <em>queued</em> or <em>processing</em> with an estimated time, and <code>200 OK</code> once complete.</p>
      </section>

      <section id="rate">
        <h2>Rate limiting & retries <a class="anchor" href="#rate">#</a></h2>
        <p>Requests are limited per API key. If you exceed the per-minute or daily limit, you’ll receive <code>429 Too Many Requests</code> and an error code indicating which limit was hit. Use exponential backoff and respect any <code>Retry-After</code> header.</p>
        <ul>
          <li><strong>Per-minute limit</strong> — enforced counter that resets every minute</li>
          <li><strong>Daily limit</strong> — enforced counter that resets every 24 hours</li>
        </ul>
        <p class="muted">On success, you’ll typically get a <code>check_id</code> to poll. During heavy load, you may see <code>status: "queued"</code> with an estimated completion time. Backoff and poll less frequently while queued.</p>
      </section>

      <section id="faq">
        <h2>FAQ <a class="anchor" href="#faq">#</a></h2>

        <h3>Can I submit JSON instead of files?</h3>
        <p>Yes. Provide <code>main_product_image</code>, <code>other_product_images[]</code>, <code>ip_images[]</code>, and <code>reference_images[]</code> as URLs or base64 strings. Keep large base64 strings out of logs in your application.</p>

        <h3>How long do checks take?</h3>
        <p>Varies by queue length and input size. While queued or processing, the API may provide an <code>estimated_completion_time</code> (in seconds). Implement backoff polling to be gentle on rate limits.</p>

        <h3>What does the legal opinion include?</h3>
        <p>A summarised assessment based on matches and court-history data, with an overall <code>risk_level</code> (High/Medium/Low) to help triage decisions.</p>

        <h3>Are results stable?</h3>
        <p>Deterministic signals (e.g., exact text/visual matches) are stable. As underlying data sources expand, later queries can yield richer results. Cache results per <code>check_id</code> in your system.</p>

        <h3>What file types are supported?</h3>
        <p>Standard image formats (<code>image/jpeg</code>, <code>image/png</code>, <code>image/webp</code>) are supported for uploads.</p>
      </section>

      <footer>
        <p>© {YEAR} IP Infringement Check API</p>
      </footer>

      <!--
        Developer Notes & Provenance (kept inline for single-file deliverable)
        - Error schema with {"error":{"error_code","message","details"}} and queue/processing states are aligned with the reference implementation, which returns
          statuses like "queued", "processing", "completed", and detailed error codes with 401/404/429 handling. :contentReference[oaicite:0]{index=0}
        - The asynchronous processing pattern and estimated completion time logic (queue length → ETA) are mirrored here to inform client polling strategies. :contentReference[oaicite:1]{index=1}
        - Results aggregation includes risk scoring and court-history metadata (e.g., last case docket/date), which informs the documented "Results schema". :contentReference[oaicite:2]{index=2}
        - Front-end form and upload behaviors referenced in the FAQ (URLs/base64 alternatives) reflect typical patterns in the provided client scripts. :contentReference[oaicite:3]{index=3} :contentReference[oaicite:4]{index=4}
      -->
    </main>
  </div>

  <!-- Prism JS -->
  <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  <script>
    // --- Theme: auto / light / dark ---
    (function(){
      const root = document.documentElement;
      const saved = localStorage.getItem("theme");
      if(saved){ root.setAttribute("data-theme", saved); }
      document.getElementById("themeToggle").addEventListener("click", () => {
        const cur = root.getAttribute("data-theme") || "auto";
        const next = cur === "light" ? "dark" : (cur === "dark" ? "auto" : "light");
        root.setAttribute("data-theme", next);
        localStorage.setItem("theme", next);
      });
    })();

    // --- Global Language Toggle ---
    const state = {
      lang: localStorage.getItem("lang") || "python"
    };

    function applyLang(){
      document.querySelectorAll('.code-sample[data-lang]').forEach(block=>{
        block.classList.toggle('show', block.dataset.lang === state.lang);
      });
      document.getElementById("btnPy").setAttribute("aria-pressed", String(state.lang==="python"));
      document.getElementById("btnJs").setAttribute("aria-pressed", String(state.lang==="js"));
    }
    document.getElementById("btnPy").addEventListener("click", ()=>{ state.lang="python"; localStorage.setItem("lang","python"); applyLang(); });
    document.getElementById("btnJs").addEventListener("click", ()=>{ state.lang="js"; localStorage.setItem("lang","js"); applyLang(); });
    applyLang();

    // --- Copy buttons ---
    document.querySelectorAll(".codebox .copy").forEach(btn=>{
      btn.addEventListener("click", async e=>{
        const pre = btn.closest(".codebox").querySelector("pre");
        const code = pre ? pre.innerText : "";
        try{
          await navigator.clipboard.writeText(code);
          const prev = btn.textContent;
          btn.textContent = "Copied!";
          setTimeout(()=>btn.textContent=prev, 1200);
        }catch(err){ console.error(err); }
      });
    });

    // --- Add anchors to headings ---
    function slugify(s){ return s.toLowerCase().replace(/[^a-z0-9]+/g,'-').replace(/(^-|-$)/g,''); }
    document.querySelectorAll("h2[id], h3[id]").forEach(h=>{
      const a = document.createElement("a");
      a.className="anchor"; a.href = `#${h.id}`; a.textContent="#"; a.setAttribute("aria-label", `Anchor to ${h.textContent}`);
      h.appendChild(a);
    });

    // --- Scroll spy for sidebar ---
    const links = [...document.querySelectorAll('aside nav a')];
    const map = Object.fromEntries(links.map(a=>[a.getAttribute('href'), a]));
    const obs = new IntersectionObserver((entries)=>{
      entries.forEach(({target, isIntersecting})=>{
        if(isIntersecting){
          const id = '#'+target.id;
          links.forEach(l=>l.classList.toggle('active', l.getAttribute('href')===id));
          history.replaceState(null, '', id);
        }
      });
    }, { rootMargin:"-40% 0px -55% 0px", threshold: [0,1] });
    document.querySelectorAll("main section[id]").forEach(s=>obs.observe(s));

    // --- Year ---
    document.querySelectorAll("footer").forEach(f=>f.innerHTML = f.innerHTML.replace("{YEAR}", new Date().getFullYear()));
  </script>
</body>
</html>
