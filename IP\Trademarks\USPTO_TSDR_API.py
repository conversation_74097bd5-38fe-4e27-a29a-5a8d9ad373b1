import os,re,time,asyncio,datetime,random,aiohttp,multiprocessing
start_time = time.time()
from bs4 import BeautifulSoup
if multiprocessing.current_process().name == 'MainProcess':
    print(f"                TSDR API BeautifulSoup after  {time.time()-start_time:.2f} seconds")

import numpy as np
from fuzzywuzzy import fuzz
from logdata import log_message # Added logdata import
import json
from IP.Trademarks.USPTO_TSDR_API_Key_manager import Api<PERSON>eyManager

# All requests are rate limited at 60 requests per API key per minute. 
# PDF, ZIP, and multi-case downloads are rate limited at four requests per API key per minute. 
# Rate limits are subject to change depending on system availability and usage.


### What works and what does not:
# When casesstatus does not work, with reg no, zip bundle might still work
# Even if status not available, image is available using the serial number
# When status is available but image is not online, it is inside (1) the status content zip and (2) the document zip in the drawing folder


### Toyota Reg no 843138 (IN_DC_1_24-cv-08880_2024-09-25_1_ 0_Exhibit_1_page26_0): the registration certificate should serial: 273449 (which does not work) but the XML show 72273449
### seria sn00381518 not found (olympic case)


# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

REG_LENGTH = 7
SER_LENGTH = 8
USPTO_API_KEYS = [os.getenv("USPTO_API_KEY"), os.getenv("USPTO_API_KEY_2")]
if not all(USPTO_API_KEYS):
    log_message("API keys not found in environment variables USPTO_API_KEY and USPTO_API_KEY_2. API calls might be limited.", level='WARNING')

ser_no_prefix_ranges = [
    (datetime.date(1870, 10, 25), datetime.date(1905, 3, 31), "70"),
    (datetime.date(1905, 4, 1), datetime.date(1955, 12, 31), "71"),
    (datetime.date(1956, 1, 1), datetime.date(1973, 8, 31), "72"),
    (datetime.date(1973, 9, 1), datetime.date(1989, 11, 15), "73"),
    (datetime.date(1989, 11, 16), datetime.date(1995, 9, 30), "74"),
    (datetime.date(1995, 10, 1), datetime.date(2000, 3, 20), "75"),
    (datetime.date(2000, 3, 21), datetime.date(2023, 12, 31), "76"),  # Paper filings
    (datetime.date(2006, 9, 14), datetime.date(2010, 3, 26), "77"),  # Internet filings
    (datetime.date(2000, 3, 21), datetime.date(2006, 9, 13), "78"),  # Internet filings
    (datetime.date(2003, 11, 3), datetime.date(2023, 12, 31), "79"),  # §66(a) filings
    (datetime.date(2010, 3, 27), datetime.date(2013, 7, 1), "85"),   # Internet filings
    (datetime.date(2013, 7, 2), datetime.date(2016, 4, 13), "86"),   # Internet filings
    (datetime.date(2016, 4, 14), datetime.date(2018, 6, 14), "87"),  # Internet filings
    (datetime.date(2018, 6, 15), datetime.date(2020, 6, 13), "88"),  # Internet filings
    (datetime.date(2020, 6, 14), datetime.date(2021, 8, 28), "90"),  # Internet filings
    (datetime.date(2021, 8, 29), datetime.date(2023, 5, 17), "97"),  # Internet filings
    (datetime.date(2023, 5, 18), datetime.date.today(), "98") # Internet filings
]

class TSDRApi:
    """
    A class to interact with the USPTO TSDR API for trademark data retrieval.
    """
    def __init__(self, api_keys=None):
        """
        Initializes the TSDRApi client.

        Args:
            api_keys (list, optional): A list of USPTO API keys. Defaults to using environment variables.
        """
        # Store the original list of keys if needed for other purposes,
        # but pass a filtered list to ApiKeyManager.        
        if api_keys is None:
            self.api_keys_list = USPTO_API_KEYS
        else:
            self.api_keys_list = api_keys
            
        # Filter out None or empty strings before passing to ApiKeyManager
        effective_api_keys = [key for key in self.api_keys_list if key]
        self.api_key_manager = ApiKeyManager(effective_api_keys)
        self.session = None # Initialize session in start method

    async def start_session(self):
        """Starts an aiohttp client session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()

    async def close_session(self):
        """Closes the aiohttp client session."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    async def _request_with_retry(self, url, request_type='light', max_attempts_per_url=4):
        """
        Handles API requests with retry logic, using ApiKeyManager for rate limiting.

        Args:
            url (str): The API endpoint URL.
            headers (dict, optional): HTTP headers. Defaults to None.
            max_attempts_per_url (int, optional): Maximum total retry attempts for this specific URL.
            request_type (str): 'heavy' or 'light', passed to ApiKeyManager.
            
        Raises:
            RuntimeError: If the API request fails after maximum retries.
        """
        last_exception = None

        retry_attempts = 0 # Initialize retry attempts counter
        while retry_attempts < max_attempts_per_url:
            current_api_key_value, current_api_key_original_index = await self.api_key_manager.get_key(request_type)

            try:
                req_headers = {}
                if current_api_key_value: # If a key was provided by the manager
                    req_headers["USPTO-API-KEY"] = current_api_key_value

                # log_message(f"Requesting URL: {url} with API Key index: {current_api_key_original_index if current_api_key_value else 'N/A'}, Attempt: {retry_attempts + 1}/{max_attempts_per_url}", level='DEBUG')
                async with self.session.get(url, headers=req_headers, ssl=True) as response:
                    if response.status == 200:
                        content = await response.read()
                        log_message(f"✅ API KEY {current_api_key_original_index if current_api_key_value else 'N/A'}: URL request successful for URL: {url}", level='INFO')
                        return content
                    elif response.status == 404:
                        log_message(f"⚠️ API returned 404 for URL: {url}", level='WARNING')
                        return None # Handle 404 as None, caller decides how to interpret
                    elif response.status == 429:
                        log_message(f"⚠️ API returned status code: 429 (Rate Limit) for URL: {url} with API Key index: {current_api_key_original_index if current_api_key_value else 'N/A'}", level='WARNING')
                        retry_after_str = response.headers.get("Retry-After")
                        retry_after_seconds = 60  # Default
                        if retry_after_str:
                            try:
                                retry_after_seconds = int(retry_after_str)
                            except ValueError:
                                log_message(f"Could not parse Retry-After header value: {retry_after_str}. Defaulting to {retry_after_seconds}s.", level='WARNING')
                        
                        if current_api_key_value: # Only report if a key was used
                            await self.api_key_manager.report_rate_limit(current_api_key_original_index, retry_after_seconds)
                        else: # No API key was used, but still rate limited (e.g. IP based)
                            log_message(f"\033[91mRate limit hit without API key for URL {url}. Waiting {retry_after_seconds} seconds.\033[0m", level='WARNING')
                            await asyncio.sleep(retry_after_seconds+5)
                        # The loop will continue, and ApiKeyManager.get_key() will handle waiting if all keys are exhausted.
                    elif response.status == 500:
                        # Check for "A virus was detected" message
                        try:
                            error_content_text = await response.text() # Read as text first to avoid content type errors
                            error_content = json.loads(error_content_text)
                            if isinstance(error_content, dict) and error_content.get("message") == "A virus was detected":
                                log_message(f"⛔ API returned 500 with 'A virus was detected' for URL: {url}. Returning None immediately.", level='ERROR')
                                return None
                        except (json.JSONDecodeError, aiohttp.ContentTypeError): # Handle non-JSON or content type issues
                            log_message(f"⚠️ API returned 500 for URL: {url}, but response was not JSON or had content type issue. Content snippet: {error_content_text[:200]}", level='WARNING')
                        
                        log_message(f"⚠️ API returned status code: 500 for URL: {url}. Will retry.", level='WARNING')
                        # For 500, add a small delay before retrying, even if get_key() doesn't impose one immediately
                        await asyncio.sleep(random.uniform(0.5, 1.5) * (retry_attempts + 1)) # Small jittered backoff
                    else:
                        log_message(f"⚠️ API returned status code: {response.status} for URL: {url}", level='WARNING')
                        response.raise_for_status() # Raise HTTPError for other unhandled  bad responses

            except (aiohttp.ClientError, aiohttp.http_exceptions.HttpProcessingError) as e:
                last_exception = e
                log_message(f"Network/HTTP error during request to {url}. Error: {e}. Attempt {retry_attempts + 1}/{max_attempts_per_url}", level='WARNING')
                # Basic exponential backoff with jitter for general network errors
                await asyncio.sleep(random.uniform(0.5, 1.5) * (retry_attempts + 1))
            except Exception as e:
                last_exception = e
                log_message(f"Unexpected error during request to {url}. Error: {e}. Attempt {retry_attempts + 1}/{max_attempts_per_url}", level='ERROR')
                await asyncio.sleep(random.uniform(0.5, 1.5) * (retry_attempts + 1))
            
            retry_attempts += 1
            if retry_attempts < max_attempts_per_url:
                log_message(f"Preparing for retry attempt {retry_attempts + 1} for URL: {url}", level='DEBUG')

        log_message(f"⚠️⚠️⚠️ API URL request failed after {max_attempts_per_url} attempts for URL: {url}. Giving up. Last error: {last_exception}", level='ERROR')
        return None # Or optionally: raise last_exception if you want to propagate it            


### Single Case: pdf, zip, html, xml ###
    async def get_status_download(self, id_number, id_key='rn', format='pdf'):
        """
        Get status information as a PDF document for a given registration or serial number.
        Value: 3/10. This is short, and the info (I would expect) is also in the xml. However, the pdf has the MarkImage as an image.
        format: pdf, zip
        """
        api_url = f"https://tsdrapi.uspto.gov/ts/cd/casestatus/{id_key}{id_number}/download.{format}" # pdf, zip
        return await self._request_with_retry(api_url, request_type='heavy')


    async def get_status_content(self, id_number, id_key='rn', format='pdf'):
        """
        Get status information as a ZIP archive for a given registration or serial number.
        Value: 6/10. This has the xml and the markImage. However, the XML is different than /info.xml, e.g. it does not include "associated marks"
        format: pdf, zip, html
        """
        api_url = f"https://tsdrapi.uspto.gov/ts/cd/casestatus/{id_key}{id_number}/content.{format}"
        request_type = 'heavy' if format in ['pdf', 'zip'] else 'light' # HTML could be light
        return await self._request_with_retry(api_url, request_type=request_type)


    async def get_status_info_xml(self, id_number, id_key='rn'):
        """
        Get status information as XML content for a given registration or serial number.
        Value: 10/10. Is this the same as just /info?

        Returns: BeautifulSoup: Parsed XML content, or None if not found.
        """

        api_url = f"https://tsdrapi.uspto.gov/ts/cd/casestatus/{id_key}{id_number}/info"
        return await self._request_with_retry(api_url, request_type='light')


### Multi case status JSON ###
    async def get_status_info_json_multi(self, id_numbers, id_key='rn'):
        """
        Get status information as JSON content for a list of registration or serial numbers.
        Value: 0/10. No trademark, no certificate.
        """
        formatted_ids = ",".join(id_numbers)
        api_url = f"https://tsdrapi.uspto.gov/ts/cd/caseMultiStatus/{id_key}?ids={formatted_ids}"
        return await self._request_with_retry(api_url, request_type='heavy') # Multi-case is heavy

### Bundles ###
    async def get_casedocs_bundle(self, id_numbers, id_key='rn', option=None, format='pdf'):
        """
        Get all case documents as a PDF bundle for a SINGLE case, for a given registration or serial number.
        Does it work for multiple cases?
        Value: 8/10, in the only example I checked, it had all 360 pages of General Motors trademarks
        doc_type: SPE (specimen == product images examples), RC (registration certificate), etc.
        Without a doc_type it returns all the documents, incl application, registration certificate, specimen images, etc. => no value
        format: pdf, zip, xml
        """

        if option and option == 'category=RC' and id_key != 'rn': # Always 'rn' for registration numbers not for serial numbers (sn)
            log_message("get_reg_certificate_pdf function is only applicable for registration numbers ('reg').", level='WARNING')
            return None

        formatted_ids = ",".join(id_numbers)

        api_url = f"https://tsdrapi.uspto.gov/ts/cd/casedocs/bundle.{format}?{id_key}={formatted_ids}"
        if option:
            api_url += f"&{option}"
        return await self._request_with_retry(api_url, request_type='heavy') # Bundles are heavy


### Process results ###
    def process_xml_content(self, content):
        """
        Process the XML content and return a dictionary with the processed data.
        """
        # Parse response
        data = {}
        soup = BeautifulSoup(content, "xml")

        data["reg_no"] =  (soup.find("ns1:RegistrationNumber").text) if soup.find("ns1:RegistrationNumber") else None  # # Trademark applied for but not granted yet: should never happen because when we search for trademarks we filter out pending trademarks
        data["text"] =  (soup.find("ns2:MarkReproduction").find("ns2:MarkVerbalElementText").text)
        data["date"] =  (soup.find("ns1:RegistrationDate").text) if soup.find("ns1:RegistrationDate") else None
        country_codes = [country_code.text for country_code in soup.find_all("ns1:IncorporationCountryCode")]
        data["country_codes"] = list(set(country_codes))
        data["nb_suits"] = sum([1 for event in soup.find_all("ns2:MarkEventCode") if event.text == "NOSUI"])
        data["application_number"] = soup.find("ns1:ApplicationNumberText").text if soup.find("ns1:ApplicationNumberText") else None
        data["ser_no"] = soup.find("ns1:ApplicationNumberText").text if soup.find("ns1:ApplicationNumberText") else None
        data["status_code"] = soup.find("ns2:MarkCurrentStatusCode").text if soup.find("ns2:MarkCurrentStatusCode") else None
        data["applicant_name"] = soup.find("ns1:OrganizationStandardName").text if soup.find("ns1:OrganizationStandardName") else None

        # New fields
        data["mark_current_status_code"] = int(soup.find("MarkCurrentStatusCode").text) if soup.find("MarkCurrentStatusCode") and soup.find("MarkCurrentStatusCode").text else None
        data["mark_feature_code"] = int(soup.find("MarkFeatureCode").text) if soup.find("MarkFeatureCode") and soup.find("MarkFeatureCode").text else None
        data["mark_standard_character_indicator"] = soup.find("MarkStandardCharacterIndicator").text == "true" if soup.find("MarkStandardCharacterIndicator") else None
        data["mark_disclaimer_text"] = [tag.text.strip() for tag in soup.find_all("ns2:MarkDisclaimerText") if tag.text and tag.text.strip()]
        data["mark_image_colour_claimed_text"] = soup.find("ns2:MarkImageColourClaimedText").text.strip() if soup.find("ns2:MarkImageColourClaimedText") and soup.find("ns2:MarkImageColourClaimedText").text else None
        data["mark_image_colour_part_claimed_text"] = soup.find("ns2:MarkImageColourPartClaimedText").text.strip() if soup.find("ns2:MarkImageColourPartClaimedText") and soup.find("ns2:MarkImageColourPartClaimedText").text else None
        data["mark_translation_statement_daily"] = soup.find("ns2:MarkTranslationText").text.strip() if soup.find("ns2:MarkTranslationText") and soup.find("ns2:MarkTranslationText").text else None
        data["mark_description_statement_daily"] = soup.find("ns2:MarkDescriptionText").text.strip() if soup.find("ns2:MarkDescriptionText") and soup.find("ns2:MarkDescriptionText").text else None
        data["national_design_code"] = [code.text for code in soup.find_all("ns2:NationalDesignCode")]

        goods_services = []
        for gs_tag in soup.find_all("ns2:GoodsServices"):
            nice_class = None
            for cls in gs_tag.find_all("ns2:GoodsServicesClassification"):
                kind_tag = cls.find("ns2:ClassificationKindCode")
                if kind_tag and kind_tag.text == "Nice":
                    class_number = cls.find("ns2:ClassNumber")
                    if class_number:
                        nice_class = int(class_number.text)
                    break

            status_code = None
            status = gs_tag.find("ns2:NationalStatusCode")
            if status:
                status_code = status.text

            first_used = gs_tag.find("ns2:FirstUsedDate")
            first_used_commerce = gs_tag.find("ns2:FirstUsedCommerceDate")

            first_used_text = first_used.text if first_used else None
            first_used_commerce_text = first_used_commerce.text if first_used_commerce else None

            goods_services.append({
                "subclass": None,
                "NiceClass": nice_class,
                "StatusCode": status_code,
                "FirstDateUsed": first_used_text,
                "FirstUsedInCommerceDate": first_used_commerce_text
            })
            
        data["goods_services"] = goods_services

        data["goods_services_text_daily"] = '; '.join(tag.get_text(strip=True) for tag in soup.find_all('GoodsServicesDescriptionText'))
        data["mark_current_status_external_description_text"] = soup.find("MarkCurrentStatusExternalDescriptionText").text if soup.find("MarkCurrentStatusExternalDescriptionText") else None

        data["associated_marks"] = []
        for mark in soup.find_all("ns2:AssociatedMark"):
            formatted_string = ""
            for child in mark.children:
                if child.name is not None:  # Check if it's a tag
                    formatted_string += f"{child.text} :"
            data["associated_marks"].append(formatted_string.strip(":").strip())

        img_location = soup.find("ns2:MarkImageBag").find("ns1:FileName").text
        pure_img_location = re.sub("[^0-9]", "", img_location)
        if pure_img_location != "":
            data["pure_img_location"] = pure_img_location
            data["image_url"] = f"https://tsdr.uspto.gov/img/{pure_img_location}/large"

        classification_obj = [cl_set.find("ns2:ClassNumber") for cl_set in soup.find_all("ns2:GoodsServicesClassification")]
        int_cls = set()
        for cl_set in classification_obj:
            if cl_set:
                try:
                    int_cls.add(int(cl_set.text))
                except:
                    log_message(f"🔥 Error: Found '{cl_set.text}' as a classification number", level='ERROR')
        data["int_cls"] = sorted(list(int_cls))

        return data
    
    def process_json_content(self, content):
        """
        Process the JSON content and return a dictionary with the processed data.
        """
        data = {}
        try:
            json_data = json.loads(content.decode('utf-8'))
            trademark = json_data.get("trademarks", [{}])[0] # Get the first trademark object
            status = trademark.get("status", {})
            parties = trademark.get("parties", {})
            gs_list = trademark.get("gsList", [])
            prosecution_history = trademark.get("prosecutionHistory", [])

            data["reg_no"] = status.get("usRegistrationNumber")
            data["text"] = status.get("markElement")
            data["date"] = status.get("usRegistrationDate")

            # Extract country codes from ownerGroups
            country_codes = set()
            owner_groups = parties.get("ownerGroups", {})
            for group_key in ["30", "20", "10"]: # Iterate through common owner group keys
                for owner in owner_groups.get(group_key, []):
                    address_state_country = owner.get("addressStateCountry", {})
                    state_country = address_state_country.get("stateCountry", {})
                    if state_country and state_country.get("code"):
                        country_codes.add(state_country["code"])
            data["country_codes"] = sorted(list(country_codes))

            # Count "NOSU" events in prosecution history
            data["nb_suits"] = sum(1 for event in prosecution_history if event.get("entryCode") == "NOSU")

            data["application_number"] = status.get("serialNumber")

            # Extract applicant name from ownerGroups
            applicant_name = None
            for group_key in ["10", "20", "30"]: # Prioritize original applicant (10), then owner at publication (20), then original registrant (30)
                if owner_groups.get(group_key):
                    applicant_name = owner_groups[group_key][0].get("name")
                    if applicant_name:
                        break
            data["applicant_name"] = applicant_name

            data["associated_marks"] = [] # No direct equivalent in the provided JSON structure

            # Image location and URL - not directly available in this JSON structure
            data["pure_img_location"] = data["application_number"]
            data["image_url"] = f"https://tsdr.uspto.gov/img/{data["application_number"]}/large"

            # Extract international classes
            int_cls = set()
            for gs_entry in gs_list:
                for int_class in gs_entry.get("internationalClasses", []):
                    if int_class.get("code"):
                        try:
                            int_cls.add(int(int_class["code"]))
                        except ValueError:
                            log_message(f"🔥 Error: Found non-numeric international class code '{int_class['code']}'", level='ERROR')
            data["int_cls"] = sorted(list(int_cls))

        except json.JSONDecodeError as e:
            log_message(f"🔥 Error decoding JSON content: {e}", level='ERROR')
            return {} # Return empty dict on JSON decode error
        except Exception as e:
            log_message(f"🔥 Unexpected error processing JSON content: {e}", level='ERROR')
            return {} # Return empty dict on other errors

        return data


    async def download_from_uspto(self, url):
        # Download an image from the USPTO website

        request_headers = {"User-Agent": ("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"),
            "Accept": "image/webp,image/apng,image/*,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Connection": "keep-alive"}

        max_retries = 5  # Increased from 3 to 5
        timeout = aiohttp.ClientTimeout(total=60)  # Increased timeout to 60 seconds

        for attempt in range(max_retries):
            try:
                # Ensure session is active
                if self.session is None or self.session.closed:
                    await self.close_session()  # Ensure clean closure
                    await self.start_session()

                async with self.session.get(url, headers=request_headers, timeout=timeout) as response:
                    if response.status != 200:
                        response.raise_for_status()
                    return await response.read()

            except asyncio.TimeoutError:
                log_message(f"⚠️ Timeout occurred on attempt {attempt+1}/{max_retries} for URL {url}", level='WARNING')
                if attempt == max_retries - 1:
                    raise RuntimeError(f"⚠️⚠️⚠️ USPTO image extraction timed out after {max_retries} attempts")
                await asyncio.sleep(2 ** attempt + random.uniform(1, 3))

            except (aiohttp.ClientError, aiohttp.http_exceptions.HttpProcessingError, RuntimeError) as e:
                log_message(f"⚠️⚠️⚠️ USPTO image extraction failed after {attempt+1}/{max_retries} attempts for URL {url}. Error: {e}", level='ERROR')

                # Close and recreate session on certain errors
                if any(err in str(e).lower() for err in ["file descriptor", "operation now in progress", "connection reset"]):
                    await self.close_session()
                    await asyncio.sleep(1)  # Brief pause before creating new session
                    await self.start_session()

                if attempt == max_retries - 1:
                    raise RuntimeError(f"⚠️⚠️⚠️ USPTO image extraction failed after {max_retries} attempts: {e}")

                # Exponential backoff with jitter
                wait_time = (2 ** attempt) + random.uniform(1, 3)
                await asyncio.sleep(wait_time)

            except Exception as e:
                log_message(f"⚠️⚠️⚠️ Unexpected error during USPTO image extraction for URL {url}: {e}", level='ERROR')
                if attempt == max_retries - 1:
                    raise RuntimeError(f"⚠️⚠️⚠️ USPTO image extraction failed with unexpected error: {e}")
                await asyncio.sleep(2 ** attempt + random.uniform(1, 3))
                
                
    async def format_ser_number(self, ser_number, case_date=None, plaintiff_names=None):
        """
        Remove non-numeric characters from the id number and pad with zeros to standard length.
        """
        if ser_number is None:
            return None
        
        if isinstance(plaintiff_names, str):
            plaintiff_names = json.loads(plaintiff_names)
            
        pure_ser_no = re.sub("[^0-9]", "", ser_number)
        pure_reg_no = None
        # Serial numbers are 8 digits: 2-digit series code + 6-digit number
        if len(pure_ser_no) != SER_LENGTH:
            # First try to find the correct serial number by plaintiff match if we have plaintiff info
            log_message(f"Serial number '{pure_ser_no}' requires plaintiff match validation to determine the 1st 2 digits")
            if plaintiff_names and case_date:        
                historical_series_codes = list(set([code for start_date, end_date, code in ser_no_prefix_ranges if end_date <= case_date])) # A series is relevant if its end_date is on or before date_to_check_against
                
                if not historical_series_codes:
                    log_message(f"No historical series codes found for date from {case_date}.")
                    return None

                # Ensure base_serial_digits is the core 6 digits
                numeric_part_of_base_ser = re.sub("[^0-9]", "", pure_ser_no)
                core_serial_digits = numeric_part_of_base_ser[-6:] if len(numeric_part_of_base_ser) >= 6 else numeric_part_of_base_ser.zfill(6)
                
                async def get_names_from_xml(candidate_sn):
                    xml_content = await self.get_status_info_xml(candidate_sn, id_key='sn')
                    if xml_content:
                        processed_data = self.process_xml_content(xml_content)
                        xml_extracted_plaintiff_name = processed_data.get("xml_plaintiff_name")
                        applicant_name = processed_data.get("applicant_name")
                        candidate_rn = processed_data.get("reg_no")
                        return candidate_sn, candidate_rn, xml_extracted_plaintiff_name, applicant_name
                    else:
                        return None, None, None, None
                        
                get_names_from_xml_tasks = []
                for series_code in historical_series_codes:
                    candidate_sn = f"{series_code}{core_serial_digits}"
                    get_names_from_xml_tasks.append(get_names_from_xml(candidate_sn))
                    
                results = await asyncio.gather(*get_names_from_xml_tasks)
                
                best_match_score = 0
                best_plaintiff_name = None
                best_applicant_name = None
                for candidate_sn, candidate_rn, xml_extracted_plaintiff_name, applicant_name in results:
                    if xml_extracted_plaintiff_name:
                        for plaintiff_name in plaintiff_names:
                            match_score = fuzz.partial_ratio(plaintiff_name.lower(), xml_extracted_plaintiff_name.lower())
                            if match_score > best_match_score:
                                best_match_score = match_score
                                pure_ser_no = candidate_sn
                                pure_reg_no = candidate_rn
                                best_plaintiff_name = plaintiff_name
                                best_applicant_name = xml_extracted_plaintiff_name
                    if applicant_name:
                        for plaintiff_name in plaintiff_names:
                            match_score = fuzz.partial_ratio(plaintiff_name.lower(), applicant_name.lower())
                            if match_score > best_match_score:
                                best_match_score = match_score
                                pure_ser_no = candidate_sn
                                pure_reg_no = candidate_rn
                                best_plaintiff_name = plaintiff_name
                                best_applicant_name = applicant_name
                
                print(f"format_ser_number: Found the best candidate: {pure_ser_no}, based on a match score of {best_match_score} between the plaintiff name {best_plaintiff_name} and the name in the XML {best_applicant_name}.")
        
                if not pure_reg_no:
                    print("\033[91m !!! ❌ Did not find a valid trademark registration number for the best serial number match! \033[0m ")
                else:
                    pure_reg_no = pure_reg_no.zfill(REG_LENGTH)
                

                
        if len(pure_ser_no) != SER_LENGTH:
            print("\033[91m !!! ❌ Did not find a valid trademark serial number by trying all combinations! \033[0m ")
            
        return pure_ser_no, pure_reg_no


def format_reg_number(id_number):
    """
    Remove non-numeric characters from the id number and pad with zeros to standard length.
    Returns None if the input contains no digits.
    """
    if id_number is None:
        return None
    
    pure_id_no_before_zfill = re.sub("[^0-9]", "", str(id_number))
    if not pure_id_no_before_zfill or len(pure_id_no_before_zfill) <= 4: # If string is empty after removing non-digits
        return None
        
    pure_id_no = pure_id_no_before_zfill.zfill(REG_LENGTH)
    return pure_id_no 