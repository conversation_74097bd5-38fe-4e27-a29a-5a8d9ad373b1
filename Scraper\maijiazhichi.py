import os
import re
import sys
import time
import logging
from datetime import datetime, timedelta
import asyncio
import aiohttp

import pandas as pd
import requests
from bs4 import BeautifulSoup
from dateutil.parser import parse
from pytz import timezone

# Add project root to Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from Common.Constants import sanitize_name
from IP.Trademarks_Bulk.trademark_db import get_db_connection, get_table_from_db
from DatabaseManagement.ImportExport import get_table_from_GZ
from Scraper.maijiazhichi_single_case import B_extract_data_from_url
from IP.Copyrights.Copyright_USCO import extract_formated_copyright_registration_number, get_info_from_USCO_using_reg_no
from Alerts.Chrome_Driver import get_driver

# --- Main Configuration ---
BASE_URL = "https://maijiazhichi.com/news/page/{}"
TABLE_NAME = "cn_websites"
LOG_FILE = os.path.join(os.path.dirname(__file__), '..', 'logs', 'maijiazhichi_scraper.log')
CHINA_TZ = timezone('Asia/Shanghai')
IMAGE_DIR = os.path.abspath(os.path.join(os.getcwd(), "..", 'Documents', 'Maijiazhichi'))

# --- Logger Setup ---
def setup_logger():
    """Set up a logger for the scraper."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(LOG_FILE),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logger()

def get_html_content(url: str, retries=3, delay=3) -> str | None:
    """
    Fetches the HTML content of a given URL with retries.

    Args:
        url: The URL to fetch.
        retries: The number of times to retry.
        delay: The delay in seconds between retries.

    Returns:
        The HTML content as a string, or None if fetching fails.
    """
    for attempt in range(retries):
        try:
            response = requests.get(url, timeout=20)
            if response.status_code == 404:
                logger.warning(f"URL {url} returned a 404 Not Found status.")
            else:
                response.raise_for_status()
            return response.text
        except requests.RequestException as e:
            logger.warning(f"Request for {url} failed (attempt {attempt + 1}/{retries}): {e}")
            if attempt < retries - 1:
                time.sleep(delay)
            else:
                logger.error(f"Failed to get HTML content for {url} after {retries} attempts.")
                return None

def _parse_date(date_str: str) -> datetime | None:
    """
    Parses a date string, which can be relative (e.g., '7小时前') or absolute (e.g., '2025年8月5日').

    Args:
        date_str: The date string to parse.

    Returns:
        A datetime object or None if parsing fails.
    """
    now = datetime.now(CHINA_TZ)
    try:
        if '前' in date_str:
            if '天' in date_str:
                days = int(re.search(r'\d+', date_str).group())
                return now - timedelta(days=days)
            elif '小时' in date_str:
                hours = int(re.search(r'\d+', date_str).group())
                return now - timedelta(hours=hours)
            elif '分钟' in date_str:
                minutes = int(re.search(r'\d+', date_str).group())
                return now - timedelta(minutes=minutes)
        else:
            # Handles format like "2025年8月5日"
            return CHINA_TZ.localize(parse(date_str.replace('年', '-').replace('月', '-').replace('日', '')))
    except (ValueError, TypeError):
        logger.error(f"Could not parse date: {date_str}")
        return None

def _extract_case_numbers(text: str) -> list[str]:
    """
    Extracts case numbers (e.g., '2025-cv-09493') from a given text.

    Args:
        text: The text to search for case numbers.

    Returns:
        A list of found case numbers.
    """
    if not text:
        return []
    return re.findall(r'\d+-cv-\d+', text, re.IGNORECASE)

def parse_html_and_extract_data(html_content: str) -> list[dict]:
    """
    Parses the HTML content and extracts data from each article.

    Args:
        html_content: The HTML content of the page.

    Returns:
        A list of dictionaries, where each dictionary represents an article.
    """
    if not html_content:
        return []

    soup = BeautifulSoup(html_content, 'html.parser')
    articles = []
    
    for item in soup.select('li.item'):
        title_element = item.select_one('h2.item-title a')
        date_element = item.select_one('span.item-meta-li.date')
        views_element = item.select_one('span.item-meta-li.views')

        if not all([title_element, date_element, views_element]):
            continue

        title = title_element.get_text(strip=True)
        url = title_element.get('href')
        date_str = date_element.get_text(strip=True)
        views_str = views_element.get_text(strip=True)

        case_numbers_title = _extract_case_numbers(title)

        if not case_numbers_title:
            continue

        posting_date = _parse_date(date_str)
        views = int(re.search(r'\d+', views_str.replace('K', '000').replace('W', '0000')).group()) if views_str else 0

        articles.append({
            'posting_date': posting_date.date() if posting_date else None,
            'docket_in_title': case_numbers_title[0] if case_numbers_title else None,
            'views': views,
            'url': url,
        })

    return articles

def create_table_if_not_exists(conn):
    """
    Creates the 'cn_websites' and 'cn_websites_files' tables if they do not already exist.

    Args:
        conn: The database connection object.
    """
    with conn.cursor() as cur:
        cur.execute(f"""
            CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
                id SERIAL PRIMARY KEY,
                posting_date DATE,
                docket_in_title TEXT,
                docket_formated TEXT,
                case_id INTEGER,
                views INTEGER,
                url TEXT,
                case_number_in_content TEXT[],
                trademarks_reg_nos TEXT[],
                copyright_reg_nos TEXT[],
                patent_reg_nos TEXT[],
                artist_url TEXT,
                source_website TEXT,
                scraped_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
        """)
        cur.execute("""
            CREATE TABLE IF NOT EXISTS cn_websites_files (
                id SERIAL PRIMARY KEY,
                filename TEXT,
                cn_websites_id INTEGER REFERENCES cn_websites(id),
                type TEXT,
                reg_no TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
        """)
        conn.commit()
        logger.info(f"Tables '{TABLE_NAME}' and 'cn_websites_files' are ready.")

async def process_article(session, conn, article, case_df, plaintiff_df, copyrights_df):
    """
    Process a single article: fetch details from its URL and insert all data into the database.
    """
    url = article.get('url')
    if not url:
        logger.warning("Skipping article with no URL.")
        return

    docket_in_title = article.get('docket_in_title')
    if not docket_in_title:
        logger.warning(f"Skipping article with no case number in title: {url}")
        return

    logger.info(f"Processing article: {url}")

    # Format docket number
    year_match = re.search(r'(\d{2,4})-cv-(\d+)', docket_in_title)
    if not year_match:
        logger.warning(f"Could not parse docket number from: {docket_in_title}")
        return

    year = year_match.group(1)[-2:]
    number = year_match.group(2).zfill(5)
    search_docket = f"{year}-cv-{number}"

    # Search in case_df
    matching_cases = case_df[case_df['docket'].str.contains(search_docket, na=False)]
    docket_formated = None
    case_id = None

    if len(matching_cases) == 1:
        docket_formated = matching_cases.iloc[0]['docket']
        case_id = matching_cases.iloc[0]['id']
    elif len(matching_cases) > 1:
        copyright_cases = matching_cases[matching_cases['nos_description'] == 'Copyrights']
        if len(copyright_cases) == 1:
            docket_formated = copyright_cases.iloc[0]['docket']
            case_id = copyright_cases.iloc[0]['id']
        else:
            # Placeholder for more complex overlap logic
            logger.warning(f"Multiple copyright cases found for {search_docket}. Placeholder needed.")
            docket_formated = "NEEDS_MANUAL_REVIEW"
    else:
        logger.warning(f"No case found for {search_docket}. Attempting to fetch after retrieving the article content")

    # Extract article content first since we need it for case processing when needed
    case_folder = sanitize_name(docket_formated) if docket_formated else sanitize_name(search_docket)
    copyright_cn_allpictures_dir = os.path.join(IMAGE_DIR, case_folder)
    os.makedirs(copyright_cn_allpictures_dir, exist_ok=True)

    plaintiff_name = "Unknown"

    json_response, downloaded_files, article_text = await B_extract_data_from_url(session, url, "maijiazhichi", docket_in_title, plaintiff_name, copyright_cn_allpictures_dir)

    if not json_response:
        logger.error(f"Failed to extract details for {url}. Skipping database insert.")
        return

    # Only attempt to fetch new case if we didn't find a matching case
    if docket_formated is None or case_id is None:
        logger.warning(f"Case was not found for {search_docket} => Attempting to fetch...")
        try:
            from Alerts.Process_Single_Docket import process_single_docket
            case_data = await process_single_docket(
                docket_number=search_docket,
                article_content=article_text,  # Use extracted article text
                db_case_df=case_df,
                plaintiff_df=plaintiff_df
            )
            docket_formated = case_data['docket']
            # Find case in database after fetching
            matching_cases = case_df[case_df['docket'] == docket_formated]
            if not matching_cases.empty:
                case_id = matching_cases.iloc[0]['id']
        except Exception as e:
            logger.error(f"Failed to fetch case {search_docket}: {e}")
            docket_formated = "FETCH_FAILED"

    # 1. Add the copyrights registration numbers identified to the "copyrights" table
    driver = None
    processed_copyrights = []
    try:
        for copy_info in json_response.get("copyrights", []):
            reg_no = copy_info.get('reg_no')
            if not reg_no or "multi" in reg_no or "no_reg" in reg_no:
                continue

            formatted_reg_no = await extract_formated_copyright_registration_number(reg_no)
            if formatted_reg_no and not copyrights_df[copyrights_df['registration_number'] == formatted_reg_no].empty:
                processed_copyrights.append(formatted_reg_no)
                continue

            if not driver:
                driver = get_driver()

            logger.info(f"Fetching USCO info for {formatted_reg_no}")
            usco_info = await get_info_from_USCO_using_reg_no(formatted_reg_no, driver)
            if usco_info:
                # Insert into copyrights table
                with get_db_connection() as conn:
                    with conn.cursor() as cur:
                        cur.execute(
                            """
                            INSERT INTO copyrights (tro, registration_number, registration_date, type_of_work, title, date_of_creation, date_of_publication, copyright_claimant, authorship_on_application, rights_and_permissions, description, nation_of_first_publication, names, certificate_status)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            ON CONFLICT (registration_number) DO NOTHING
                            """,
                            (
                                True,
                                usco_info.get("registration_number"),
                                usco_info.get("registration_date"),
                                usco_info.get("type_of_work"),
                                usco_info.get("title"),
                                usco_info.get("date_of_creation"),
                                usco_info.get("date_of_publication"),
                                usco_info.get("copyright_claimant"),
                                usco_info.get("authorship_on_application"),
                                usco_info.get("rights_and_permissions"),
                                usco_info.get("description"),
                                usco_info.get("nation_of_first_publication"),
                                usco_info.get("names"),
                                "Missing",
                            )
                        )
                logger.info(f"Inserted a new copyright into database")
                processed_copyrights.append(usco_info.get("registration_number"))

    finally:
        if driver:
            driver.quit()


    # 2. Insert the article details into the "cn_websites" table
    with conn.cursor() as cur:
        cur.execute(
            f"""
            INSERT INTO {TABLE_NAME} (
                posting_date, docket_in_title, docket_formated, case_id, views, url,
                case_number_in_content, trademarks_reg_nos, copyright_reg_nos,
                patent_reg_nos, artist_url, source_website
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id
            """,
            (
                article.get('posting_date'),
                docket_in_title,
                docket_formated,
                int(case_id) if case_id is not None else None,
                int(article.get('views')) if article.get('views') is not None else None,
                url,
                json_response.get("case_numbers"),
                json_response.get("trademarks"),
                processed_copyrights,
                json_response.get("patents"),
                json_response.get("artist_url"),
                "maijiazhichi"
            )
        )
        new_article_id = cur.fetchone()[0]
        conn.commit()

    logger.info(f"Successfully processed and inserted article: {url} with ID: {new_article_id}")


    # 3. Insert the files downloaded from the article page into cn_websites_files
    copyrights_info = {item['identifier']: item['reg_no'] for item in json_response.get("copyrights", []) if item.get('identifier')}

    with conn.cursor() as cur:
        for identifier, (filepath, _) in downloaded_files.items():
            reg_no_raw = copyrights_info.get(identifier)
            reg_no_formatted = None
            if reg_no_raw:
                if reg_no_raw.lower() == 'multi':
                    file_type = 'multi'
                else:
                    file_type = 'single'
                    reg_no_formatted = await extract_formated_copyright_registration_number(reg_no_raw)
            else:
                file_type = 'single' # Or some other default

            cur.execute(
                """
                INSERT INTO cn_websites_files (filename, cn_websites_id, type, reg_no)
                VALUES (%s, %s, %s, %s)
                """,
                (os.path.basename(filepath), new_article_id, file_type, reg_no_formatted)
            )
        conn.commit()
    logger.info(f"Inserted {len(downloaded_files)} file records for article ID {new_article_id}")



async def main_async():
    """Main async function to run the scraper."""
    logger.info("Starting maijiazhichi.com scraper.")
    
    conn = get_db_connection()
    if conn is None:
        logger.error("Could not establish a database connection. Exiting.")
        return

    create_table_if_not_exists(conn)
    
    try:
        case_df = get_table_from_GZ("tb_case", force_refresh=False)
        plaintiff_df = get_table_from_GZ("tb_plaintiff", force_refresh=False)
        copyrights_df = get_table_from_db("copyrights")
        existing_df = get_table_from_db(TABLE_NAME)
        logger.info(f"Loaded {len(existing_df)} existing records from '{TABLE_NAME}'.")
    except Exception as e:
        logger.error(f"Failed to load existing data: {e}")
        existing_df = pd.DataFrame()

    stop_scraping = False
    page_num = 1
    new_articles_processed = 0

    async with aiohttp.ClientSession() as session:
        while not stop_scraping:
            url = BASE_URL.format(page_num)
            logger.info(f"Scraping page {page_num}: {url}")

            html_content = get_html_content(url)
            if not html_content:
                page_num += 1
                continue

            if "404 - 页面未找到" in html_content:
                logger.info("Reached the last page.")
                break

            articles = parse_html_and_extract_data(html_content)
            if not articles:
                logger.warning(f"No articles found on page {page_num}.")
                page_num += 1
                continue

            for article in articles:
                is_existing = False
                if not existing_df.empty and 'url' in existing_df.columns and article.get('url'):
                    if (existing_df['url'] == article['url']).any():
                        is_existing = True
                
                if is_existing:
                    logger.info(f"Found an existing article ({article.get('url')}). Stopping scraper.")
                    stop_scraping = True
                    continue
                else:
                    await process_article(session, conn, article, case_df, plaintiff_df, copyrights_df)
                    new_articles_processed += 1
                    # Add to existing_df to avoid re-processing in the same run
                    existing_df = pd.concat([existing_df, pd.DataFrame([article])], ignore_index=True)

            if stop_scraping:
                break
                
            page_num += 1

    logger.info(f"Processed and inserted {new_articles_processed} new articles.")
    conn.close()
    logger.info("Scraper finished.")

def main():
    asyncio.run(main_async())

if __name__ == '__main__':
    main()