import os
import shutil
import pandas as pd
from tqdm import tqdm
import sys
import imagehash
from PIL import Image
from Common.Constants import local_ip_tro_folder
from IP.Trademarks_Bulk.trademark_db import get_db_connection
from qdrant_client import QdrantClient, models
import uuid

sys.path.append(os.getcwd())

# Qdrant Configuration
QDRANT_API_URL = os.getenv("QDRANT_URL", "http://localhost:6333")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY", None)
QDRANT_COLLECTION_IP_ASSETS = "IP_Assets_Optimized"
qdrant_client = QdrantClient(url=QDRANT_API_URL, api_key=QDRANT_API_KEY)

def update_copyright_production_status(filename, status):
    """Updates the production status of a copyright file in the database."""
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        sql = "UPDATE copyrights_files SET production = %s WHERE filename = %s"
        cursor.execute(sql, (status, filename))
        conn.commit()
    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error updating production status for {filename}: {e}")
    finally:
        if conn:
            conn.close()

def find_and_copy_duplicates(hash_size=16, threshold_critical=25, threshold_moderate=35):
    """
    Identifies visually similar images and processes them based on similarity thresholds.
    """
    production_folder = os.path.join(local_ip_tro_folder, "copyrights", "Production")
    all_folder = os.path.join(local_ip_tro_folder, "copyrights", "All")
    duplicate_folder = os.path.join(local_ip_tro_folder, "copyrights", "DuplicateOneOff")
    os.makedirs(duplicate_folder, exist_ok=True)
    duplicate_folder2 = os.path.join(local_ip_tro_folder, "copyrights", "DuplicateOneOff2")
    os.makedirs(duplicate_folder2, exist_ok=True)

    all_hashes = {}
    for filename in tqdm(os.listdir(all_folder), desc="Hashing 'All' folder images"):
        if filename.lower().endswith('.webp') and filename.split('_')[1].startswith("VA"):
            try:
                file_path = os.path.join(all_folder, filename)
                with Image.open(file_path) as img:
                    img_hash = imagehash.phash(img, hash_size=hash_size)
                    all_hashes[img_hash] = filename
            except (IndexError, FileNotFoundError):
                continue

    pair_index = 1
    processed_files = set()

    for prod_filename in tqdm(os.listdir(production_folder), desc="Processing Production files"):
        if prod_filename.lower().endswith('.webp') and prod_filename.split('_')[1].startswith("MD") and prod_filename not in processed_files:
            prod_file_path = os.path.join(production_folder, prod_filename)
            try:
                with Image.open(prod_file_path) as img:
                    prod_hash = imagehash.phash(img, hash_size=hash_size)
                  
                for all_hash, all_filename in all_hashes.items():
                    if all_filename not in processed_files:
                        hash_diff = prod_hash - all_hash
                        
                        if hash_diff < threshold_critical:
                            print(f"Critical match found: {prod_filename} and {all_filename} (Diff: {hash_diff})")
                            
                            shutil.copy(os.path.join(all_folder, all_filename), os.path.join(production_folder, all_filename))
                            os.remove(prod_file_path)

                            processed_files.add(prod_filename)
                            processed_files.add(all_filename)
                            pair_index += 1
                            break

                        elif threshold_critical <= hash_diff < threshold_moderate:
                            print(f"Moderate match found: {prod_filename} and {all_filename} (Diff: {hash_diff})")
                            shutil.copy(prod_file_path, os.path.join(duplicate_folder, f"{pair_index}_MD.webp"))
                            shutil.copy(os.path.join(all_folder, all_filename), os.path.join(duplicate_folder, f"{pair_index}_VA.webp"))
                            
                            processed_files.add(prod_filename)
                            processed_files.add(all_filename)
                            pair_index += 1
                            break
                        elif threshold_critical <= hash_diff < threshold_moderate+5:
                            print(f"Moderate match found: {prod_filename} and {all_filename} (Diff: {hash_diff})")
                            shutil.copy(prod_file_path, os.path.join(duplicate_folder2, f"{pair_index}_MD.webp"))
                            shutil.copy(os.path.join(all_folder, all_filename), os.path.join(duplicate_folder2, f"{pair_index}_VA.webp"))
                            
                            processed_files.add(prod_filename)
                            processed_files.add(all_filename)
                            pair_index += 1
                            break

            except (IndexError, FileNotFoundError, Exception) as e:
                print(f"Error processing {prod_filename}: {e}")
                continue
            
    print("All done")

if __name__ == "__main__":
    find_and_copy_duplicates()